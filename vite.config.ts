// vite.config.js
import legacy from '@vitejs/plugin-legacy'
import { defineConfig } from 'vite'

export default defineConfig({
    base: './',
    build: {
        outDir: 'dist',
        assetsDir: 'assets',
        rollupOptions: {
            output: {
                chunkFileNames: "static/js/[name].js",
                entryFileNames: "static/js/[name].js",
                assetFileNames: "static/[ext]/[name].[ext]"
            },
            input: {
                main: 'index.html',
            },
        },
        sourcemap: true,
    },
    plugins: [
        legacy({
            targets: ['defaults', 'ie >= 11', 'chrome 52'],
            additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
            renderLegacyChunks:true,
            polyfills:[
                'es.symbol',
                'es.array.filter',
                'es.array.flat',
                'es.promise',
                'es.promise.finally',
                'es/map',
                'es/set',
                'es.array.for-each',
                'es.object.define-properties',
                'es.object.define-property',
                'es.object.get-own-property-descriptor',
                'es.object.get-own-property-descriptors',
                'es.object.keys',
                'es.object.to-string',
                'web.dom-collections.for-each',
                'esnext.global-this',
                'esnext.string.match-all'
            ]
        }),
    ],
    server: {
        open: true,
        port: 3005,
        host: '0.0.0.0'
    },
    publicDir: 'public',
})
