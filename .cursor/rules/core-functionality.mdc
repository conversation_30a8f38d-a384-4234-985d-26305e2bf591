---
description: 
globs: 
alwaysApply: false
---
# Core Functionality

This project is a Smart Clause application for contract review, built with TypeScript and integrating with OnlyOffice for document editing and display.

## Main Components

* **OnlyOfficeManager**: [src/ts/OnlyOfficeManager.ts](mdc:src/ts/OnlyOfficeManager.ts) - Manages the OnlyOffice document editor integration. Handles document loading, comment management, and interaction with the document.

* **RiskItemsManager**: [src/ts/RiskItemsManager.ts](mdc:src/ts/RiskItemsManager.ts) - Manages the risk assessment interface using Alpine.js. Provides functionality for displaying, filtering, and interacting with contract risk items.

## Key Features

* **Document Viewing/Editing**: Uses OnlyOffice to display and interact with contract documents
* **Risk Assessment**: Identifies and manages risk items within contracts
* **Comment Management**: Allows adding, viewing, and managing comments on contract clauses
* **Navigation**: Provides functionality to jump between risk items and their corresponding document sections

## Application Entry Point

The main entry point is [src/main.ts](mdc:src/main.ts), which exposes the RiskItemsManager and OnlyOfficeManager classes to the global window object for use in web applications.

## Example Usage

See [examples/risk-items-example.html](mdc:examples/risk-items-example.html) for a complete example of how to integrate and use the Smart Clause functionality in a web application.
