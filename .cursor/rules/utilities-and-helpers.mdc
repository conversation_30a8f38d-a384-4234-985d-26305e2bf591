---
description: 
globs: 
alwaysApply: false
---
# Utilities and Helpers

This project includes several utility functions and helper classes to support the main functionality.

## Utility Functions

The [src/ts/utils.ts](mdc:src/ts/utils.ts) file contains utility functions used throughout the application:

* `isValidJson`: Validates if a string is valid JSON
* `safeJsonParse`: Safely parses JSON with error handling
* `throttle`: Limits the rate at which a function can be called

## Message Management

The [src/ts/AppMessage.ts](mdc:src/ts/AppMessage.ts) class provides functionality for managing application messages and communication between components.

## Alpine.js Integration

The RiskItemsManager uses Alpine.js for reactivity. The integration is handled within the [src/ts/RiskItemsManager.ts](mdc:src/ts/RiskItemsManager.ts) file, which registers Alpine.js components and manages their lifecycle.
