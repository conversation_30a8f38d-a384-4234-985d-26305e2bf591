---
description: 
globs: 
alwaysApply: false
---
# Project Structure and Technology Stack Guide

This project is a front-end application for contract review called Smart Clause, integrating OnlyOffice for document editing and Alpine.js for UI components.

## Key Files and Directories

*   **`index.html`**: [index.html](mdc:index.html) is the main HTML entry point for the application.
*   **`src/`**: This directory contains the core source code of the application.
    *   **`src/main.ts`**: [src/main.ts](mdc:src/main.ts) is the main entry point that exports the core classes.
    *   **`src/ts/`**: Contains the TypeScript implementation files.
        *   **`src/ts/OnlyOfficeManager.ts`**: [src/ts/OnlyOfficeManager.ts](mdc:src/ts/OnlyOfficeManager.ts) - Core class for OnlyOffice integration.
        *   **`src/ts/RiskItemsManager.ts`**: [src/ts/RiskItemsManager.ts](mdc:src/ts/RiskItemsManager.ts) - Core class for risk assessment UI.
        *   **`src/ts/AppMessage.ts`**: [src/ts/AppMessage.ts](mdc:src/ts/AppMessage.ts) - Messaging utility class.
        *   **`src/ts/utils.ts`**: [src/ts/utils.ts](mdc:src/ts/utils.ts) - Utility functions.
        *   **`src/ts/types/`**: Contains TypeScript type definitions.
            *   **`src/ts/types/onlyoffice.ts`**: [src/ts/types/onlyoffice.ts](mdc:src/ts/types/onlyoffice.ts) - OnlyOffice related types.
    *   **`src/css/`**: Contains CSS stylesheets.
*   **`examples/`**: Contains example implementations.
    *   **`examples/risk-items-example.html`**: [examples/risk-items-example.html](mdc:examples/risk-items-example.html) - Example usage of the risk items functionality.
*   **`public/`**: This directory is for static assets that are served directly.
*   **`dist/`**: Contains the built application files (generated during build).

## Configuration Files

*   **`vite.config.ts`**: [vite.config.ts](mdc:vite.config.ts) - Configuration for Vite build tool.
*   **`tsconfig.json`**: [tsconfig.json](mdc:tsconfig.json) - TypeScript compiler configuration.
*   **`package.json`**: [package.json](mdc:package.json) - Project dependencies and scripts.
*   **`pnpm-workspace.yaml`**: [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml) - PNPM workspace configuration.

## Technology Stack

*   **TypeScript**: The primary programming language for the application.
*   **OnlyOffice**: Used for document processing and display features, integrated via the OnlyOfficeManager class.
*   **Alpine.js**: Used for building reactive UI components, particularly in the risk assessment interface.
*   **Vite**: Build tool for bundling and development server.

## Smart Clause Functionality

The core purpose of this application is to provide a user interface for reviewing contracts with smart clause features:

1. **Document Viewing/Editing**: Using OnlyOffice to display and interact with contract documents.
2. **Risk Assessment**: Identifying and managing risk items within contracts.
3. **Comment Management**: Adding, viewing, and managing comments on contract clauses.
4. **Navigation**: Jumping between risk items and their corresponding document sections.
