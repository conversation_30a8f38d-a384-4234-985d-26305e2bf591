---
description: 
globs: 
alwaysApply: false
---
# Data Types and Interfaces

This project uses TypeScript interfaces to define the structure of data used throughout the application.

## OnlyOffice Types

The file [src/ts/types/onlyoffice.ts](mdc:src/ts/types/onlyoffice.ts) contains TypeScript interfaces for OnlyOffice integration:

* `OnlyOfficeConfig`: Configuration options for the OnlyOffice document editor
* `OnlyOfficeInstance`: Interface for the OnlyOffice document editor instance
* `CommentData`: Structure for comment data within documents
* `DocData`: Structure for document data including risk assessment information
* `OnlyOfficeManagerConfig`: Configuration for the OnlyOfficeManager class

## RiskItemsManager Types

The [src/ts/RiskItemsManager.ts](mdc:src/ts/RiskItemsManager.ts) file defines interfaces for risk assessment:

* `RiskItemsCallbacks`: Callback functions for risk item interactions
* `RiskItemFilter`: Filtering options for risk items

## Utility Types

The [src/ts/AppMessage.ts](mdc:src/ts/AppMessage.ts) file contains types related to application messaging and communication.
