---
description:
globs:
alwaysApply: false
---
# Build and Configuration

This project uses Vite as its build tool and TypeScript for type checking.

## Build Configuration

* **Vite Config**: [vite.config.ts](mdc:vite.config.ts) - Contains the Vite configuration for building the project, including plugins and build options.

* **TypeScript Config**: [tsconfig.json](mdc:tsconfig.json) - Contains TypeScript compiler options and configuration.

## Package Management

* **Package.json**: [package.json](mdc:package.json) - Lists project dependencies and defines npm scripts for development, building, and previewing the application.

* **PNPM Workspace**: [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml) - PNPM workspace configuration for managing packages.

## Build Scripts

The following npm scripts are available:

* `npm run dev`: Starts the development server
* `npm run build`: Builds the production version
* `npm run build:dev`: Builds the development version
* `npm run build:prod`: Builds the production version
* `npm run preview`: Previews the built application

## Dependencies

Main dependencies include:

* **Alpine.js**: Used for reactivity in the risk assessment interface
* **TypeScript**: For static typing
* **Vite**: For building and bundling the application
