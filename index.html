<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + TS</title>
    <style>
      body {
          font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
          margin: 0;
          padding: 0;
          display: flex;
          height: 100vh;
      }
      
      .editor-container {
          flex: 2;
          height: 100%;
          border-right: 1px solid #e0e0e0;
      }
      
      .risk-items-container {
          flex: 1;
          height: 100%;
          overflow-y: auto;
          padding: 0 10px;
          background-color: #f9f9f9;
      }
      
      .risk-items-header {
          position: sticky;
          top: 0;
          background-color: #f9f9f9;
          padding: 15px 0;
          border-bottom: 1px solid #e0e0e0;
          z-index: 10;
      }
      
      .risk-items-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
      }
      
      .filters {
          margin: 15px 0;
          padding: 10px;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      
      .filter-group {
          margin-bottom: 10px;
      }
      
      .filter-title {
          font-weight: 600;
          margin-bottom: 5px;
          color: #333;
      }
      
      .filter-options {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
      }
      
      .filter-checkbox {
          display: flex;
          align-items: center;
          margin-right: 10px;
      }
      
      .filter-checkbox input {
          margin-right: 4px;
      }
      
      .search-box {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 10px;
      }
      
      .container-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          margin-top: 15px;
          border-bottom: 1px solid #e0e0e0;
          cursor: pointer;
      }
      
      .container-title {
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;
      }
      
      .container-count {
          background-color: #eee;
          border-radius: 12px;
          padding: 2px 8px;
          font-size: 12px;
          margin-left: 8px;
      }
      
      .container-toggle {
          color: #666;
          font-size: 18px;
          transition: transform 0.2s;
      }
      
      .container-toggle.expanded {
          transform: rotate(180deg);
      }
      
      .risk-item-card {
          margin: 10px 0;
          padding: 12px;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      
      .risk-item-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
      }
      
      .risk-item-title {
          font-weight: 600;
          color: #333;
      }
      
      .risk-level {
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 12px;
          font-weight: 600;
      }
      
      .level-critical {
          background-color: #ffebee;
          color: #d32f2f;
      }
      
      .level-warning {
          background-color: #fff8e1;
          color: #ff8f00;
      }
      
      .level-info {
          background-color: #e8f5e9;
          color: #388e3c;
      }
      
      .risk-item-content {
          margin-bottom: 10px;
          color: #555;
      }
      
      .risk-item-quote {
          margin: 10px 0;
          padding: 8px;
          background-color: #f5f5f5;
          border-left: 3px solid #ddd;
          color: #666;
          font-style: italic;
      }
      
      .risk-item-actions {
          display: flex;
          gap: 8px;
          margin-top: 10px;
      }
      
      .action-button {
          padding: 6px 12px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: background-color 0.2s;
      }
      
      .btn-primary {
          background-color: #1976d2;
          color: white;
      }
      
      .btn-primary:hover {
          background-color: #1565c0;
      }
      
      .btn-secondary {
          background-color: #f5f5f5;
          color: #333;
      }
      
      .btn-secondary:hover {
          background-color: #e0e0e0;
      }
      
      .btn-danger {
          background-color: #f44336;
          color: white;
      }
      
      .btn-danger:hover {
          background-color: #d32f2f;
      }
      
      .risk-item-details {
          margin-top: 10px;
          padding-top: 10px;
          border-top: 1px dashed #ddd;
      }
      
      .details-row {
          display: flex;
          margin-bottom: 5px;
      }
      
      .details-label {
          flex: 1;
          color: #666;
      }
      
      .details-value {
          flex: 2;
          color: #333;
      }
      
      .highlight-item {
          animation: highlight-pulse 2s;
      }
      
      @keyframes highlight-pulse {
          0% { box-shadow: 0 0 0 0 rgba(255, 230, 0, 0.7); }
          70% { box-shadow: 0 0 0 10px rgba(255, 230, 0, 0); }
          100% { box-shadow: 0 0 0 0 rgba(255, 230, 0, 0); }
      }
      
      .comment-content {
          margin-top: 10px;
          padding: 10px;
          background-color: #f5f5f5;
          border-radius: 4px;
      }
      
      .comment-textarea {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          min-height: 60px;
          margin-bottom: 8px;
      }
      
      .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
      }
      
      .demo-controls {
          padding: 15px;
          background-color: #f0f0f0;
          border-bottom: 1px solid #ddd;
      }
      
      .demo-note {
          margin-top: 10px;
          padding: 10px;
          background-color: #fffde7;
          border-left: 4px solid #ffd600;
          font-size: 14px;
          color: #5d4037;
      }
    </style>
  </head>
  <body x-data="riskItemsData">
    <div id="onlyoffice-container" class="onlineBoxs form">
        <!--this文档-->
    </div>
    <div id="risk-items-container" class="onlineReaults">
        <div class="resTop">
             <ul class="resTopTab">
                 <li class="on">合同风险提示</li>
                 <li>条款搜索</li>
             </ul>
        </div>
        <!--合同风险提示start-->
        <div class="resMain">
                <div class="resOperation" style="overflow: visible;">
                         <div class="resOp">
                                <button id="saveBtn" class="resBtn_haslock" style="margin-right: 0px;">												
                                    <div class="i_b5"></div><p class="i_b5_txt">保存</p>
                                </button>
                         </div>
                         <div class="resOp">
                                <button class="resBtn">
                                     <div class="i_b1"></div><p class="i_b1_txt">下载</p>
                                </button>
                                <div class="resTips">
                                     <a>
                                         <div>→ 获取附带审批意见版本2</div>
                                         <p>（包含批注和修订）</p>
                                     </a>
                                     <a>
                                         <div>→ 获取清洁版本2</div>
                                         <p>（不包含批注和修订）</p>
                                     </a>
                                </div>
                         </div>
                        <button class="resBtn" @click="showFilterPopup = true">
                             <div class="i_b2"></div><p>筛选</p>
                        </button>
                        <a class="resBtn">
                             <div class="i_b4" style="margin-top: 15px !important;"></div><p>重新分析</p>
                        </a>
                        <button class="resBtn">
                             <div class="i_b3"></div><p>管理订阅</p>
                        </button>                     
                 </div>
                <div style="clear:both;"></div>
                <div class="retlists">
                    <!-- 缺失条款容器 -->
                    <div class="ret" :class="{ 'open': isMissingClauseContainerExpanded }">
                        <h2 class="zk" @click="toggleMissingClauseContainer()">
                            <i></i>缺失条款摘要 <span x-text="missingClauseItems.length"></span>
                        </h2>
                        <div class="retboxs" :class="{ 'addheight': isMissingClauseContainerExpanded }">
                            <!-- 使用x-for循环遍历缺失条款项 -->
                            <template x-for="(item, index) in missingClauseItems" :key="item.commentId || `missing-${index}`">
                                <div class="retmenus" :data-comment-id="item.commentId" 
                                     :class="{ 'highlight-item': isItemSelected(item) }"
                                     @click="jumpToComment(item)" style="cursor: pointer;">
                                    <div class="retHeader" x-text="item.name || '未命名条款'"></div>
                                    <div class="rmu">(全局)不利于：
                                        <span x-show="item.partyType === 1" class="color000000">甲乙双方</span>
                                        <span x-show="item.partyType === 2" class="color000000">甲方</span>
                                        <span x-show="item.partyType === 3" class="color000000">乙方</span>
                                    </div>
                                    <div class="rmu">
                                        风险等级：
                                        <!-- AURA-X: Modify - 根据riskLevels配置动态显示风险等级名称. Approval: 寸止(ID:1704444800). -->
                                        <span x-text="config.riskLevels.find(r => r.objectId === String(item.level))?.name || '未知等级'" 
                                              :class="'color' + (item.level === 1 ? 'FF2F00' : '43CF7C')"></span>
                                    </div>
                                    <div class="rmu"><span x-show="showSign">【智合同】</span>风险提示：</div>
                                    <div class="rmu3" x-text="item.hint"></div>
                                    <div class="rmu"><span x-show="showSign">【智合同】</span>建议增加条款示例：</div>
                                    <div class="rmus" x-text="item.suggestion || '无建议'"></div>
                                    <div class="rmu" x-show="showRelationLaws && item.relationLaws && item.relationLaws.length > 0"><span x-show="showSign">【智合同】</span>法律依据：</div>
									<ul class="rmu4" x-show="showRelationLaws && item.relationLaws && item.relationLaws.length > 0">
										<template x-for="(law, lawIndex) in (item.relationLaws || [])" :key="law.lawId || `missing-law-${item.commentId}-${lawIndex}`">
											<li><a :href="config.relationUrl + '&id=' + law.lawId + '&ruleId=' + law.ruleItemId" target="_blank" data-toggle="tooltip" data-placement="bottom" :title="law.content" x-text="law.lawName"></a></li>
										</template>
									</ul>
                                    <div x-show="showRelationCase && item.relationCase" class="rmu"><span x-show="showSign">【智合同】</span>参考案例：</div>
                                            <template x-if="showRelationCase && item.relationCase">
                                                <ul class="rmu4">
                                                    <li><a :href="config.relationUrl + '&id=' + (item.relationCase?.objectId || '')" target="_blank" x-text="item.relationCase?.name || ''"></a></li>
                                                </ul>
                                        
                                            </template>
                                    <div class="rmu" x-show="showKnowledgeSource && item.knowledgeSource">知识来源：<span class="color2d8cf0" x-text="item.knowledgeSource"></span></div>
                                    <div class="allbtns">                       
                                        <button class="findbtn itemBtn" data-toggle="tooltip" data-placement="top" 
                                                title="一键插入" @click.stop="insertText(item)" :disabled="isReadOnly"></button>
                                        <button class="findbtn findbtn3" data-toggle="tooltip" data-placement="top" 
                                                title="删除" @click.stop="deleteComment(item)" :disabled="isReadOnly"></button>
                                        <button class="findbtn findbtn2" data-toggle="tooltip" data-placement="top" 
                                                title="备注" @click.stop="toggleCommentContent(item)"></button>
                                        <div class="editRadio" x-show="showEditRadio">
                                            <label><input type="radio" :checked="item.itemNote && item.itemNote.status === 1" 
                                                          @click.stop="if(!item.itemNote) item.itemNote = { content: '', status: 1 }; else item.itemNote.status = 1; saveItem(item)" :disabled="isReadOnly"><span>确认</span></label>
                                            <label><input type="radio" :checked="item.itemNote && item.itemNote.status === 0" 
                                                          @click.stop="if(!item.itemNote) item.itemNote = { content: '', status: 0 }; else item.itemNote.status = 0; saveItem(item)" :disabled="isReadOnly"><span>忽略</span></label>
                                        </div>                   
                                    </div>
                                    <template x-if="commentVisibilityMap[item.commentId]">
                                        <div class="notestext">
                                            <label :for="'note-' + item.commentId" class="sr-only">备注内容</label>
                                            <textarea class="sureInput" :id="'note-' + item.commentId" :name="'note-' + item.commentId" x-model="item.itemNote.content" placeholder="请输入备注" @click.stop :disabled="isReadOnly"></textarea>
                                            <div class="notesSave" x-show="!isReadOnly">
                                                <button @click.stop="saveItem(item)">保存</button>
                                            </div>
                                        </div>
                                    </template>
                                    <div class="handle" :class="item.itemNote && item.itemNote.status === 1 ? 'no' : 'yes'"></div>									
                                </div>
                            </template>
                            <!-- 数据加载中时显示 -->
                            <div class="retmenus" x-show="loading">
                                <div class="rmu loading" style="text-align: center;">缺失条款数据加载中...</div>
                            </div>
                            <!-- 当没有缺失条款时显示 -->
                            <div class="retmenus" x-show="!loading && missingClauseItems.length === 0">
                                <div class="rmu loading" style="text-align: center;">没有找到缺失条款</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 风险提示容器 -->
                    <div class="ret" :class="{ 'open': isRiskPromptContainerExpanded }">
                        <h2 class="zk" @click="toggleRiskPromptContainer()">
                            <i></i>风险提示 <span x-text="riskPromptItems.length"></span>
                        </h2>
                        <div class="retboxs" :class="{ 'addheight': isRiskPromptContainerExpanded }">
                            <!-- 使用x-for循环遍历风险提示项 -->
                            <template x-for="(item, index) in riskPromptItems" :key="item.commentId || `risk-${index}`">
                                <div class="retmenus" :data-comment-id="item.commentId"
                                     :class="{ 'highlight-item': isItemSelected(item) }"
                                     @click="jumpToComment(item)" style="cursor: pointer;">
                                    <div class="retHeader" x-text="item.name || '风险提示'"></div>
                                    <div class="rmu">(全局)不利于：
                                        <span x-show="item.partyType === 1" class="color000000">甲乙双方</span>
                                        <span x-show="item.partyType === 2" class="color000000">甲方</span>
                                        <span x-show="item.partyType === 3" class="color000000">乙方</span>
                                    </div>
                                    <div class="rmu">
                                        风险等级：
                                        <!-- AURA-X: Modify - 根据riskLevels配置动态显示风险等级名称. Approval: 寸止(ID:1704444800). -->
                                        <span x-text="config.riskLevels.find(r => r.objectId === String(item.level))?.name || '未知等级'" 
                                              :class="'color' + (item.level === 1 ? 'FF2F00' : '43CF7C')"></span>
                                    </div>
                                    <div class="rmu"><span x-show="showSign">【智合同】</span>风险提示：</div>
                                    <div class="rmu3" x-text="item.hint"></div>
                                    <div class="rmu" x-show="item.quoteText">引用文本：</div>
                                    <div class="rmus" x-show="item.quoteText" x-text="item.quoteText"></div>
                                    <div class="rmu" x-show="item.suggestion"><span x-show="showSign">【智合同】</span>建议：</div>
                                    <div class="rmus" x-show="item.suggestion" x-text="item.suggestion"></div>
                                    <div class="rmu" x-show="showRelationLaws && item.relationLaws && item.relationLaws.length > 0"><span x-show="showSign">【智合同】</span>法律依据：</div>
									<ul class="rmu4" x-show="showRelationLaws && item.relationLaws && item.relationLaws.length > 0">
										<template x-for="(law, lawIndex) in (item.relationLaws || [])" :key="law.lawId || `law-${item.commentId}-${lawIndex}`">
											<li><a :href="config.relationUrl + '&id=' + law.lawId + '&ruleId=' + law.ruleItemId" target="_blank" data-toggle="tooltip" data-placement="bottom" :title="law.content" x-text="law.lawName"></a></li>
										</template>
									</ul>
                                    <div x-show="showRelationCase && item.relationCase" class="rmu"><span x-show="showSign">【智合同】</span>参考案例：</div>
                                            <template x-if="showRelationCase && item.relationCase">
                                                <ul class="rmu4">
                                                    <li><a :href="config.relationUrl + '&id=' + (item.relationCase?.objectId || '')" target="_blank" x-text="item.relationCase?.name || ''"></a></li>
                                                </ul>
                                        
                                            </template>
                                    <div class="rmu" x-show="showKnowledgeSource && item.knowledgeSource">知识来源：<span class="color2d8cf0" x-text="item.knowledgeSource"></span></div>
                                    <div class="allbtns">                       
                                        <button class="findbtn itemBtn" x-show="item.suggestion" data-toggle="tooltip" 
                                                data-placement="top" title="一键插入" @click.stop="insertText(item)" :disabled="isReadOnly"></button>
                                        <button class="findbtn findbtn3" data-toggle="tooltip" data-placement="top" 
                                                title="删除" @click.stop="deleteComment(item)" :disabled="isReadOnly"></button>
                                        <button class="findbtn findbtn2" data-toggle="tooltip" data-placement="top" 
                                                title="备注" @click.stop="toggleCommentContent(item)"></button>
                                        <div class="editRadio" x-show="showEditRadio">
                                            <label><input type="radio" :checked="item.itemNote && item.itemNote.status === 1" 
                                                          @click.stop="if(!item.itemNote) item.itemNote = { content: '', status: 1 }; else item.itemNote.status = 1; saveItem(item)" :disabled="isReadOnly"><span>确认</span></label>
                                            <label><input type="radio" :checked="item.itemNote && item.itemNote.status === 0" 
                                                          @click.stop="if(!item.itemNote) item.itemNote = { content: '', status: 0 }; else item.itemNote.status = 0; saveItem(item)" :disabled="isReadOnly"><span>忽略</span></label>
                                        </div>                   
                                    </div>
                                    <template x-if="commentVisibilityMap[item.commentId]">
                                        <div class="notestext">
                                            <textarea class="sureInput" :id="'note-' + item.commentId" :name="'note-' + item.commentId" x-model="item.itemNote.content" placeholder="请输入备注" @click.stop :disabled="isReadOnly"></textarea>
                                            <div class="notesSave" x-show="!isReadOnly">
                                                <button @click.stop="saveItem(item)">保存</button>
                                            </div>
                                        </div>
                                    </template>
                                    <div class="handle" :class="item.itemNote && item.itemNote.status === -1 ? 'no' : 'yes'"></div>									
                                </div>
                            </template>
                            <!-- 数据加载中时显示 -->
                            <div class="retmenus" x-show="loading">
                                <div class="rmu loading" style="text-align: center;">风险提示数据加载中...</div>
                            </div>
                            <!-- 当没有风险提示时显示 -->
                            <div class="retmenus" x-show="!loading && riskPromptItems.length === 0">
                                <div class="rmu loading" style="text-align: center;">没有找到风险提示</div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
        <!--合同风险提示end-->
        
        
        <!--条款搜索start-->
        <div class="resMain resMain-tk  hide">
            <ul class="resTopChild nav nav-tabs">
                <li class="active"><a>按分类查询</a></li>
                <li><a>按标签查询</a></li>
            </ul>
            
            <!--合同分类-->
            <div class="tab_tablelists_media">
                <div class="resOperation">
                    <button class="schtBtn">生成合同</button>
                    <button class="schtBtn">保存合同</button>
                </div>
                <div class="searchlst">
                    
                    <div class="main_search_inputs">
                        <select class="m_s_s">
                            <option>全部</option>
                            <option>全部11111111111111111111111</option>
                            <option>全部</option>
                            <option>全部</option>
                        </select>
                        <input type="text" placeholder="请输入条款关键字" class="m_s_p">
                        <a class="m_s_a">查询</a>
                    </div>
                    
                    <!--查询1,无结果时显示第一种-->
                    <div class="main_search_conditions">
                        <h4>合同分类</h4>
                        <div class="conditionsBox tt_boxs">
                            <div class="qrm-Box1 qrm-Box">
                                <ul class="qrm-list-1 qrm-list">
                                    <li class="on"><span class="qs">全部</span></li>
                                    <li>
                                        <span class="qs">合同合同合同合同合同合同合同合同合同合同合同合同1<i class="sr"></i></span>
                                        <ul class="li-zi-1">
                                            <li>
                                                <span>全部</span>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span class="qs">合同2<i class="sr"></i></span>
                                        <ul class="li-zi-1">
                                            <li>
                                                <span>合同合同合同合同合同合同合同合同合同合同合同合同合同2-1</span>
                                            </li>
                                            <li>
                                                <span>合同2-2</span>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="qrm-Box2 qrm-Box">
                                <ul class="qrm-list-2 qrm-list">
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    
                    <!--查询2,和结果一起显示-->
                    <div class="main_search_conditions main_search_conditions_haslists" style="display: none;">
                        <h4 class="scr_if-poptitle">合同分类：<a class="qrm-input">全部</a></h4>
                        <div class="conditionsBox conditionsBox-littles">
                            <div class="qrm-Box1">
                                <ul class="qrm-list-1 qrm-list">
                                    <li class="on"><span class="qs">全部</span></li>
                                    <li>
                                        <span class="qs">合同合同合同合同合同合同合同合同合同合同合同合同1<i class="sr"></i></span>
                                        <ul class="li-zi-1">
                                            <li>
                                                <span>全部</span>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span class="qs">合同2<i class="sr"></i></span>
                                        <ul class="li-zi-1">
                                            <li>
                                                <span>合同合同合同合同合同合同合同合同合同合同合同合同合同2-1</span>
                                            </li>
                                            <li>
                                                <span>合同2-2</span>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="qrm-Box2">
                                <ul class="qrm-list-2 qrm-list">
                                </ul>
                            </div>
                            <a class="scr_up">收起</a>
                        </div>
                    </div>
                    
                    
                    <!--结果-->
                    <div class="getConLists">
                        <ul>
                            <li>
                                <div class="gl-num">1.</div>
                                <div class="glnDiv">
                                    <div class="glnTop">
                                        <label><a>(系统范本)</a>支出类-货物类-其他货物</label>
                                        <a class="zk_iframescreen color2d8cf0"><b>展开</b><i></i></a>
                                    </div>
                                    <div class="generateConList-tag">
                                        <ul class="search-tags">
                                            <li>包装</li>
                                        </ul>
                                        
                                    </div>
                                    <div class="glnCon many-txt">
                                        合同<span>包装</span>合同采购xxx合同采购xxx合同采购<span>安装与调试</span>xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx合同采购xxx
                                    </div>
                                    <div class="glnBot">
                                        <span>2020-05-30 20:00:00&nbsp;|&nbsp;合同名称：华中商品买卖合同合同</span>
                                    </div>
                                    <div class="oneclick"><button class="findbtn">一键插入</button></div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    
                    
                </div>
            </div>
            
            <!--标签-->
            <div class="tab_tablelists_media hide">
                <div class="searchlst">
                    
                    <div class="main_search_inputs">
                        <select class="m_s_s">
                            <option>全部</option>
                        </select>
                        <input type="text" placeholder="请输入条款关键字" class="m_s_p">
                        <a class="m_s_a">查询</a>
                    </div>
                    
                </div>
                
                
            </div>

        </div>
        <!--条款搜索end-->
    </div>
    <!-- 筛选弹出框 -->
    <div class="screen_progressBar" x-show="showFilterPopup" >
        <div class="screen_progressBar_main">
            <div class="screen_thisboxs">
                <h4>筛选</h4>
                <div class="s_boxs">
                    <span>风险等级：</span>
                    <label class="checkbox-inline">
                        <input id="_level_1" class="level-checkbox" type="checkbox" value="1" x-model="filters.level"> 严重
                    </label>
                    <label class="checkbox-inline">
                        <input id="_level_2" class="level-checkbox" type="checkbox" value="2" x-model="filters.level"> 一般
                    </label>
                    <label class="checkbox-inline">
                        <input id="_level_3" class="level-checkbox" type="checkbox" value="3" x-model="filters.level"> 建议
                    </label>
                </div>
                <div class="s_boxs">
                    <span>不利于：</span>
                    <label class="checkbox-inline">
                        <input id="_partyType_2" class="partyType-checkbox" type="checkbox" value="2" x-model="filters.partyType"> 不利于甲方
                    </label>
                    <label class="checkbox-inline">
                        <input id="_partyType_3" class="partyType-checkbox" type="checkbox" value="3" x-model="filters.partyType"> 不利于乙方
                    </label>
                    <label class="checkbox-inline">
                        <input id="_partyType_1" class="partyType-checkbox" type="checkbox" value="1" x-model="filters.partyType"> 不利于甲乙双方
                    </label>
                </div>
                <div class="s_boxs">
                    <span>其他：</span>
                    <label class="checkbox-inline">
                        <input id="subscribe-checkBox" value="1" type="checkbox" x-model="onlyShowSubscribed"> 只显示我订阅的内容
                    </label>
                </div>
            </div>
            <div class="popup-button">
                <a id="siftClickBtn" class="public-btn colorffffff borderRadius" @click="applyFilters(); showFilterPopup = false;">提交</a>
                <a id="cancelBtn" class="public-btn-qx borderRadius" @click="showFilterPopup = false;">取消</a>
            </div>
        </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // 模拟数据，实际应用中应从 OnlyOfficeManager 获取
      const mockRiskItems = [
          {
              commentId: "comment-001",
              objectId: "obj-001",
              type: "missingClause",
              level: 1,
              name: "保密条款",
              hint: "合同中缺少保密条款，建议添加",
              suggestion: "双方应对在合作过程中获知的对方的商业秘密和其他保密信息承担保密义务。除非法律要求或得到对方书面同意，任何一方不得向第三方披露上述信息。",
              knowledgeSource: "《合同法》第四十三条",
              partyType: 1,
              role: "legal",
              itemNote: { content: "", status: 0 }
          },
          {
              commentId: "comment-002",
              objectId: "obj-002",
              type: "missingClause",
              level: 2,
              name: "违约责任条款",
              hint: "合同中缺少明确的违约责任条款",
              suggestion: "任何一方违反本合同约定，应向守约方支付合同总金额 10% 的违约金。如违约金不足以弥补守约方损失的，违约方还应赔偿守约方的实际损失。",
              knowledgeSource: "《合同法》第一百零七条",
              partyType: 1,
              role: "legal",
              itemNote: { content: "", status: 0 }
          },
          {
              commentId: "comment-003",
              objectId: "obj-003",
              type: "riskPrompt",
              level: 1,
              name: "付款条件风险",
              hint: "付款条件对甲方过于不利，建议重新协商",
              quoteText: "乙方有权在交付产品前要求甲方支付全部款项，否则有权拒绝交付。",
              partyType: 2,
              role: "finance",
              relationLaws: [
                  { lawId: "law-001", lawName: "《合同法》第六十六条", content: "当事人互负债务，没有先后履行顺序的，应当同时履行。" }
              ],
              itemNote: { content: "已与对方协商，对方同意修改为分期付款", status: 1 }
          },
          {
              commentId: "comment-004",
              objectId: "obj-004",
              type: "riskPrompt",
              level: 2,
              name: "知识产权条款",
              hint: "知识产权归属不明确，可能导致后续纠纷",
              quoteText: "双方在合作过程中产生的知识产权归双方共有。",
              partyType: 3,
              role: "legal",
              relationLaws: [
                  { lawId: "law-002", lawName: "《专利法》第八条", content: "专利申请权和专利权可以转让。" }
              ],
              itemNote: { content: "", status: 0 }
          },
          {
              commentId: "comment-005",
              objectId: "obj-005",
              type: "riskPrompt",
              level: 3,
              name: "管辖权条款",
              hint: "建议明确约定争议解决方式和管辖法院",
              quoteText: "",
              partyType: 1,
              role: "legal",
              itemNote: { content: "", status: 0 }
          }
      ];

      // 使用真实的 OnlyOfficeManager 和 RiskItemsManager 类
      document.addEventListener('DOMContentLoaded', function() {
          // 等待 main.ts 将类暴露到全局
          const initApp = () => {
              if (!window.OnlyOfficeManager || !window.RiskItemsManager) {
                  console.log('等待 OnlyOfficeManager 和 RiskItemsManager 类加载...');
                  setTimeout(initApp, 100);
                  return;
              }
              
              console.log('OnlyOfficeManager 和 RiskItemsManager 类已加载');
              
              // 创建配置对象
              const config = {
                  containerId: 'onlyoffice-container',
                  rightMenuId: 'risk-items-container',
                  config: {
                      documentType: 'word',
                      document: {
                          fileType: 'docx',
                          key: 'demo-document',
                          title: '示例合同.docx',
                          url: 'https://example.com/demo-contract.docx'
                      },
                      editorConfig: {
                          mode: 'edit',
                          lang: 'zh-CN'
                      }
                  },
                  isShowComment: true,
                  isDevelopment: true,
                  subscribeRoles: 'legal,finance,reviewer',
                  riskLevels: { critical: 1, warning: 2, info: 3 },
                  relationUrl: 'https://example.com/api/relations',
                  showSign: true,
                  showKnowledgeSource: true,
                  showEditRadio: true // 控制是否显示确认/忽略单选按钮
              };
              
              // 右边菜单jsp 异步返回创建回调函数
              const callbacks = {
                  onItemJump: async (item) => {
                      console.log(`回调: onItemJump 被调用，objectId: ${item.objectId}`);
                      return true;
                  },
                  onItemDelete: async (item) => {
                      console.log(`回调: onItemDelete 被调用，commentId: ${item.commentId}`);
                      return true;
                  },
                  onTextInsert: async (hint, item) => {
                      console.log(`回调: onTextInsert 被调用，插入文本: ${hint.substring(0, 30)}...`);
                      return true;
                  },
                  onSaveItem: async (item) => {
                      console.log(`回调: onSaveItem 被调用，保存风险项: ${item.commentId}`);
                      // 模拟保存成功
                      return true;
                  }
              };
              
              try {
                  // 初始化管理器实例
                  const riskItemsManager = new window.RiskItemsManager(config, callbacks);
                  const onlyOfficeManager = new window.OnlyOfficeManager(config);
                 
                  
                  // 初始化编辑器和风险项管理器
                  riskItemsManager.init();
                  onlyOfficeManager.init();
                  
                  
                  
                  
                  // 监听自定义事件,onlyoffice-comments-loaded
                  document.addEventListener('onlyoffice-comments-loaded', function(event) {
                      console.log('捕获到 onlyoffice-comments-loaded 事件:', event.detail);

                      // 从事件中获取风险项数据
                      if (event.detail && event.detail.docData) {
                          // 使用真实数据，setData方法内部会自动设置loading状态
                          riskItemsManager.setData(event.detail.docData);
                          console.log('已从 OnlyOffice 加载风险项数据，共', event.detail.docData.length, '项');
                      }
                  });
                  
                  // 监听自定义事件,右边菜单上的审核事项被点击
                  document.addEventListener('item-jump', function(event) {
                      console.log('捕获到 item-jump 事件:', event.detail);
                      onlyOfficeManager.scrollToCommentByObjectId(event.detail.objectId);
                  });
                  
                  // 监听自定义事件,右边菜单上 点击插入文本
                  document.addEventListener('text-insert', function(event) {
                      console.log('捕获到 text-insert 事件:', event.detail);
                      onlyOfficeManager.applySuggestion(event.detail.item.suggestion);
                  });
                  
                  // 监听自定义事件,右边菜单上 审核事项保存 item-save
                  document.addEventListener('item-save', function(event) {
                      console.log('捕获到 item-save 事件:', event.detail);
                  });
                  
                  // 监听自定义事件,onlyoffice-comment-clicked
                  document.addEventListener('onlyoffice-comment-clicked', function(event) {
                      console.log('捕获到 onlyoffice-comment-clicked 事件:', event.detail);
                      riskItemsManager.scrollToItemByObjectId(event.detail.firstCommentObjectId);
                  });
                  
                  console.log('应用初始化完成');
              } catch (error) {
                  console.error('初始化失败:', error);
                  console.error(error);
              }
          };
          
          // 开始初始化
          initApp();
      });
    </script>
  </body>
</html>
