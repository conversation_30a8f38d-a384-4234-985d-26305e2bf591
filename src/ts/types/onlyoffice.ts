/**
 * onlyoffice.ts
 * OnlyOffice 相关的接口和类型定义
 */

/**
 * 数据类型
 */
export interface OnlyOfficeManagerConfig {
  config: OnlyOfficeConfig; // 编辑器配置
  containerId: string; // 容器 ID
  rightMenuId: string; // 右边菜单 id
  isShowComment: boolean; // 初始化是否显示备注
  isDevelopment: boolean; // 是否为开发环境
  subscribeRoles: string; // 订阅角色权限字符串
  riskLevels: {objectId:string,name:string}[]; // 风险等级配置
  relationUrl: string; // 法律依据和参考案例跳转页面 URL
  showSign: boolean; // 是否显示智合同标题
  showKnowledgeSource: boolean; // 是否显示知识来源
  showEditRadio: boolean; // 是否显示确认/忽略单选按钮
}

/**
 * OnlyOffice 配置接口
 */
export interface OnlyOfficeConfig {
  document: {
    fileType: string;
    key: string;
    title: string;
    url: string;
    permissions: {
      comment: boolean;
      download: boolean;
      edit: boolean;
      print: boolean;
      review: boolean;
    };
  };
  documentType: 'word' | 'cell' | 'slide';
  editorConfig: {
    mode: 'view' | 'edit' | 'review' | 'comment' | 'fillForms';
    lang?: string;
    callbackUrl?: string;
    user?: {
      id: string;
      name: string;
    };
    customization?: {
      compactToolbar?: boolean;
      feedback?: boolean;
      forceSave?: boolean;
      showReviewChanges?: boolean;
      autosave?: boolean;
    };
  };
  events?: {
    onAppReady?: (event: any) => void;
    onDocumentStateChange?: (event: any) => void;
    onRequestSaveAs?: (event: any) => void;
    onRequestInsertImage?: (event: any) => void;
    onRequestClose?: () => void;
    onDocumentReady?: (event: any) => void;
    onInfo?: (event: any) => void;
    onEditorReady?: (event: any) => void;
  };
}

/**
 * OnlyOffice 编辑器实例接口
 */
export interface OnlyOfficeInstance {
  destroyEditor: () => void;
  downloadAs: (format: string) => void;
  insertImage: (options: any) => void;
  setReviewChange: (enable: boolean) => void;
  showSaveAsDialog: () => void;
}

/**
 * 备注数据接口 
 */
export interface CommentData {
  id: string;// 备注id
  initials: string;// 初始化数据
  positionInfo: any;// 位置信息
  quoteText: string;// 引用文本
  text: string;// 备注文本
  userName: string;// 用户名称
  userId: string;// 用户id
  hidden: boolean;// 是否隐藏
  color: number[];// 颜色
  activeColor: number[];// 激活颜色
  hidePopover: boolean;// 是否隐藏弹窗
}

/**
 * 法律依据数据格式
 */
export interface RelationLaws {
  lawId: string;// 法律id
  lawName: string;// 法律名称
  ruleItemName: string;// 规则名称
  ruleItemId: string;// 规则id
  content: string;// 内容
}

/**
 * 参考案例数据格式  
 */
export interface RelationCase {
  objectId: string;
  name: string;// 名称
  code: string;// 编码
}

/**
 * 风险提示数据格式
 */
export interface DocData {
  commentId: string; // onlyoffice 备注id
  role: string; // 角色
  hasSubscribe?: boolean; // 是否订阅
  level: number; // 1严重 2 一般 3 建议风险等级
  levelName?: string;//中文名字
  hint: string; // 风险提示
  partyType: number; // 不利于 1甲乙双方 2 甲方 3 乙方
  adoptSuggestion: boolean; // 是否已经标记
  indexID: number; // 序列
  name: string; // 缺失条款
  knowledgeSource: string; // 知识来源
  suggestion: string; // 建议增加条款
  quoteText: string; // 真实的文本
  objectId?: string; // 服务器id
  type: number;// 类型  风险是 1 缺失是 2
  relationLaws?: RelationLaws[]; // 法律依据跳转页面url
  relationCase?: RelationCase; // 参考案例跳转页面url
  typeName?: string; // 违规类型
  itemNote?: { content: string; status?: number }; // 0已处理，1已处理 -1 默认值
}

/**
 * 全局 Window 接口扩展
 */
declare global {
  interface Window {
    DocsAPI: {
      DocEditor: new (container: string, config: OnlyOfficeConfig) => OnlyOfficeInstance;
    };
  }
} 