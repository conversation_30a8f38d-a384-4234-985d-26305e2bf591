// 添加 Alpine 全局对象的接口定义
declare global {
  interface Window {
    Alpine?: any; // Alpine.js 全局对象
  }
}

/**
 * RiskItemsManager.ts
 * 使用 Alpine.js 构建风险项页面的管理类
 * 独立的风险项管理组件，通过公用函数设置数据
 */
import Alpine from 'alpinejs';
import type { DocData, OnlyOfficeManagerConfig } from './types/onlyoffice';

export interface RiskItemsCallbacks {
  onItemJump?: (item: DocData) => Promise<boolean> | boolean; // 跳转回调
  onItemDelete?: (item: DocData) => Promise<boolean> | boolean; // 删除回调
  onRefresh?: () => Promise<DocData[]> | DocData[]; // 刷新数据回调
  onTextInsert?: (suggestion: string, item: DocData) => Promise<boolean> | boolean; // 插入文本回调
  onSaveItem?: (item: DocData) => Promise<boolean> | boolean; // 保存风险项回调
}

export interface RiskItemFilter {
  level?: string[]; // 改为 string[] 类型，因为 HTML checkbox 返回的是字符串
  role?: string[];//角色
  partyType?: string[];//不利于 1甲乙双方 2 甲方 3 乙方
  searchText?: string;
}


/**
 * 风险项管理器类
 * 使用 Alpine.js 构建响应式的风险项界面
 */
export class RiskItemsManager {
  private config: OnlyOfficeManagerConfig;
  private container: HTMLElement | null = null;
  private isInitialized: boolean = false;
  private data: DocData[] = []; // 内部数据存储
  private static alpineRegistered: boolean = false; // 静态标记，避免重复注册
  private callbacks: RiskItemsCallbacks;
  private isReadOnly: boolean = false; // 是否为只读模式
  
  constructor(config: OnlyOfficeManagerConfig,callbacks: RiskItemsCallbacks) {
    this.config = config;
    this.callbacks = callbacks;
    this.container = document.getElementById(config.rightMenuId);
    
    // 根据 editorConfig.mode 设置只读状态
    this.isReadOnly = config.config.editorConfig.mode === 'view';
    
    if (!this.container) {
      throw new Error(`容器元素 #${config.rightMenuId} 未找到`);
    }
  }

  /**
   * 获取只读状态
   * @returns 是否为只读模式
   */
  public getIsReadOnly(): boolean {
    return this.isReadOnly;
  }

  /**
   * 设置风险项数据
   * @param data 风险项数据数组
   */
  public setData(data: DocData[]): void {
    this.data = [...data];
    if (this.isInitialized) {
      this.triggerDataUpdate();
    }
    console.log('风险项数据已更新，共', this.data.length, '项');
  }

  /**
   * 获取当前数据
   * @returns 当前风险项数据数组
   */
  public getData(): DocData[] {
    return [...this.data];
  }

  /**
   * 添加风险项
   * @param item 要添加的风险项
   */
  public addItem(item: DocData): void {
    this.data.push(item);
    if (this.isInitialized) {
      this.triggerDataUpdate();
    }
    console.log('已添加风险项:', item.commentId);
  }

  /**
   * 根据 commentId 移除风险项
   * @param commentId 要移除的风险项ID
   */
  public removeItem(commentId: string): boolean {
    const initialLength = this.data.length;
    this.data = this.data.filter(item => item.commentId !== commentId);
    const removed = this.data.length < initialLength;
    
    if (removed && this.isInitialized) {
      this.triggerDataUpdate();
      console.log('已移除风险项:', commentId);
    }
    return removed;
  }

  /**
   * 根据 objectId 移除所有相关风险项
   * @param objectId 对象ID
   */
  public removeItemsByObjectId(objectId: string): number {
    const initialLength = this.data.length;
    this.data = this.data.filter(item => item.objectId !== objectId);
    const removedCount = initialLength - this.data.length;
    
    if (removedCount > 0 && this.isInitialized) {
      this.triggerDataUpdate();
      console.log('已移除', removedCount, '个风险项，objectId:', objectId);
    }
    
    return removedCount;
  }

  /**
   * 更新风险项
   * @param item 要更新的风险项
   * @returns 是否成功更新
   */
  public updateItem(item: DocData): boolean {
    const index = this.data.findIndex(i => i.commentId === item.commentId);
    if (index === -1) {
      console.warn(`未找到要更新的风险项: ${item.commentId}`);
      return false;
    }
    
    this.data[index] = {...item};
    if (this.isInitialized) {
      this.triggerDataUpdate();
    }
    console.log('已更新风险项:', item.commentId);
    return true;
  }

  /**
   * 清空所有数据
   */
  public clearData(): void {
    this.data = [];
    if (this.isInitialized) {
      this.triggerDataUpdate();
    }
    console.log('已清空所有风险项数据');
  }

  /**
   * 触发数据更新
   * @private
   */
  private triggerDataUpdate(): void {
    const updateEvent = new CustomEvent('risk-items-data-updated', {
      detail: { data: this.data }
    });
    
    // 始终向 document 分发事件，确保当 x-data 绑定在 body 上时能接收到
    document.dispatchEvent(updateEvent);
    
    // 如果容器元素存在且不是 document.body，也向容器元素分发事件（保持向后兼容性）
    if (this.container && this.container !== document.body) {
      this.container.dispatchEvent(updateEvent);
    }
  }

  /**
   * 初始化风险项管理器
   */
  public init(): boolean {
    if (!this.container) {
      console.error('容器元素未找到');
      return false;
    }

    try {
      // 注册 Alpine.js 数据组件（只注册一次）
      if (!RiskItemsManager.alpineRegistered) {
        this.registerAlpineComponent();
        RiskItemsManager.alpineRegistered = true;
      }

      // 启动 Alpine.js（如果还未启动）
      Alpine.start();
      
      this.isInitialized = true;
      console.log('RiskItemsManager 初始化成功');
      return true;
    } catch (error) {
      console.error('RiskItemsManager 初始化失败:', error);
      return false;
    }
  }

  /**
   * 注册 Alpine.js 数据组件
   */
  private registerAlpineComponent(): void {
    const config = this.config;
    const dataManager = this; // 获取类实例引用
  

    // 定义 Alpine.js 数据和方法
    Alpine.data('riskItemsData', () => ({
      // 数据状态
      loading: true, // 初始状态为加载中
      allItems: [] as DocData[],//所有风险项
      filteredItems: [] as DocData[],//筛选后的风险项
      expandedComments: {} as Record<string, boolean>, // 存储展开状态的对象，key 为 commentId
      commentVisibilityTrigger: 0, // 强制重新计算备注显示状态的触发器
      isMissingClauseContainerExpanded: false, // 缺失条款容器是否展开，默认为折叠
      isRiskPromptContainerExpanded: true, // 风险提示容器是否展开，默认为展开
      selectedItemId: '', // 当前选中的风险项ID
      showFilterPopup: false, // 是否显示筛选弹出框
      onlyShowSubscribed: false, // 是否只显示已订阅的内容
      isReadOnly: dataManager.getIsReadOnly(), // 是否为只读模式
      
      // 配置选项
      showKnowledgeSource: config.showKnowledgeSource ?? true,
      showSign: config.showSign ?? true, // 添加showSign配置，控制是否显示"【智合同】"文本
      showEditRadio: config.showEditRadio ?? true, // 添加showEditRadio配置，控制是否显示确认/忽略单选按钮
      showRelationLaws: true, // 默认显示关联法规
      showRelationCase: true, // 默认显示关联案例
      config: {
        riskLevels: config.riskLevels,
        relationUrl: config.relationUrl || 'https://example.com/api/relations' // 添加relationUrl配置
      },
      // 筛选配置
      filters: {
        searchText: '',
        level: [] as string[],//风险等级
        role: [] as string[],//角色
        partyType: [] as string[]//不利于 1甲乙双方 2 甲方 3 乙方
      },
      
      // 排序配置 - 简化为只支持字段选择，默认升序
      sortConfig: {
        field: 'level' as 'level' | 'indexID' | 'name' | 'commentId'
      },

      // 获取缺失条款类型的风险项
      get missingClauseItems() {
        return this.filteredItems.filter(item => item.type === 2);
      },
      
      // 获取风险提示类型的风险项
      get riskPromptItems() {
        return this.filteredItems.filter(item => item.type === 1);
      },

      // 获取备注显示状态映射（计算属性，确保响应式更新）
      get commentVisibilityMap() {
        // 触发器依赖，确保响应式更新
        void this.commentVisibilityTrigger;
        
        const map: Record<string, boolean> = {};
        
        for (const item of this.allItems) {
          const commentId = item.commentId;
          
          // 如果已经有明确的展开状态设置（用户点击过备注按钮），使用该状态
          if (this.expandedComments.hasOwnProperty(commentId)) {
            map[commentId] = !!this.expandedComments[commentId];
          } else {
            // 如果没有明确设置状态，根据备注内容决定默认显示状态
            map[commentId] = !!(item.itemNote && item.itemNote.content && item.itemNote.content.trim());
          }
        }
        
        console.log('计算 commentVisibilityMap:', map, 'trigger:', this.commentVisibilityTrigger);
        return map;
      },

      // 初始化方法
      init() {
        console.log('Alpine.js 风险项组件初始化');
        this.loadData();
        
        // 监听容器元素和document上的数据更新事件
        const eventHandler = (event: Event) => {
          console.log('检测到数据更新事件');
          const customEvent = event as CustomEvent;
          this.allItems = [...customEvent.detail.data];
          this.applyFilters();
          // 数据更新完成，设置加载状态为false
          this.loading = false;
        };
        
        // 当x-data绑定在body上时，需要监听document上的事件
        document.addEventListener('risk-items-data-updated', eventHandler);
        
        // 始终在当前元素上添加事件监听
        this.$el.addEventListener('risk-items-data-updated', eventHandler);
      },

      // 加载数据
      async loadData() {
        this.loading = true;
        try {
          // 从内部数据管理器获取数据
          this.allItems = dataManager.getData();
          console.log('加载风险项数据:', this.allItems.length, '项');
          
          // 应用筛选和排序
          this.applyFilters();
        } catch (error) {
          console.error('加载风险项数据失败:', error);
        }
      },

      // 刷新数据 - 移除回调依赖，仅重新加载内部数据
      async refreshData() {
        console.log('刷新风险项数据');
        this.loadData();
      },

      // 应用筛选
      applyFilters() {
        let items = [...this.allItems];

        // 文本搜索
        if (this.filters.searchText) {
          const searchText = this.filters.searchText.toLowerCase();
          items = items.filter(item => 
            (item.hint && item.hint.toLowerCase().includes(searchText)) ||
            (item.suggestion && item.suggestion.toLowerCase().includes(searchText)) ||
            (item.name && item.name.toLowerCase().includes(searchText)) ||
            (item.quoteText && item.quoteText.toLowerCase().includes(searchText))
          );
        }

        // 风险等级筛选
        if (this.filters.level && this.filters.level.length > 0) {
          const levels = this.filters.level.map((l: string) => parseInt(l));
          items = items.filter(item => levels.includes(item.level));
        }

        // 角色筛选
        if (this.filters.role && this.filters.role.length > 0) {
          items = items.filter(item => 
            item.role && this.filters.role!.includes(item.role)
          );
        }

        // 当事人类型筛选
        if (this.filters.partyType && this.filters.partyType.length > 0) {
          items = items.filter(item => 
            item.partyType && this.filters.partyType!.includes(item.partyType.toString())
          );
        }

        // 只显示已订阅的内容
        if (this.onlyShowSubscribed) {
          items = items.filter(item => this.isItemSubscribeVisible(item));
        }

        this.filteredItems = items;
      },

      // 获取等级样式类
      getLevelClass(level: number): string {
        switch (level) {
          case 1: return 'level-critical';
          case 2: return 'level-warning';
          case 3: return 'level-info';
          default: return 'level-unknown';
        }
      },

      // 获取等级名称
      getLevelName(level: number): string {
        switch (level) {
          case 1: return '严重';
          case 2: return '一般';
          case 3: return '建议';
          default: return '未知';
        }
      },

      // 跳转到备注 - 直接调用外部函数
      async jumpToComment(item: DocData) {
        console.log(`跳转到备注: ${item.commentId}`);
        
        // 检查 objectId 是否存在
        if (!item.objectId) {
          console.warn(`风险项 ${item.commentId} 没有 objectId`);
          return false;
        }
        
        // 调用外部提供的跳转函数
        if (dataManager.callbacks.onItemJump) {
          try {
            const result = await dataManager.callbacks.onItemJump(item);
            // 回调执行完成后发送自定义消息
            if (result) {
              this.$dispatch('item-jump', { 
                objectId: item.objectId, 
                item: item 
              });
              console.log(`已跳转到备注并发送消息，objectId: ${item.objectId}`);
              return true;
            }else{
              console.warn(`跳转到备注失败: ${item.commentId}`);
              return false;
            }
          } catch (error) {
            console.error('跳转到备注失败:', error);
            return false;
          }
        } else {
          // 如果没有提供回调函数，直接发送自定义消息
          this.$dispatch('item-jump', { 
            objectId: item.objectId, 
            item: item 
          });
          console.log(`已直接发送跳转消息，objectId: ${item.objectId}`);
          return true;
        }
      },

      // 删除备注 - 直接调用外部函数
      async deleteComment(item: DocData) {
        // 如果是只读模式，则不允许删除
        if (this.isReadOnly) {
          console.warn('当前为只读模式，无法删除备注');
          return;
        }

        if (!confirm(`确定要删除备注 "${item.name || item.hint || item.commentId}" 吗？`)) {
          return;
        }

        try {
          // 直接调用外部提供的删除函数
          if (dataManager.callbacks.onItemDelete) {
            const result = await dataManager.callbacks.onItemDelete(item);
            if (result) {
              // 从本地数据中移除
              dataManager.removeItem(item.commentId);
              console.log(`已删除备注: ${item.commentId}`);
            }
          } else {
            // 如果没有提供删除函数，则直接从本地数据中移除
            dataManager.removeItem(item.commentId);
            console.log(`已删除备注: ${item.commentId}`);
          }
        } catch (error) {
          console.error('删除备注失败:', error);
          alert('删除备注失败，请重试');
        }
      },

      // 滚动到风险项
      scrollToItem(item: DocData) {
        console.log(`滚动到风险项: ${item.commentId}`);
        
        // 确保项目在筛选后的列表中可见
        const isVisible = this.filteredItems.some(i => i.commentId === item.commentId);
        if (!isVisible) {
          // 如果项目不可见，清除筛选条件
          console.log('风险项不在当前筛选列表中，清除筛选条件');
          this.filters = {
            searchText: '',
            level: [],
            role: [],
            partyType: []
          };
          this.applyFilters();
        }
        
        // 确保风险项所在的容器是展开的
        this.ensureContainerExpanded(item);
        
        // 等待DOM更新后执行滚动
        this.$nextTick(() => {
          // 注意：需要在HTML模板的风险项卡片上添加 data-comment-id 属性
          // 例如：<div class="risk-item-card" :data-comment-id="item.commentId">
          
          // 查找对应的DOM元素
          const itemElement = document.querySelector(`.risk-item-card[data-comment-id="${item.commentId}"]`);
          if (itemElement) {
            // 滚动到元素位置
            itemElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // 设置选中的风险项ID
            this.selectedItemId = item.commentId;
            
            // 3秒后自动清除选中状态
            setTimeout(() => {
              if (this.selectedItemId === item.commentId) {
                this.selectedItemId = '';
              }
            }, 3000);
            
            console.log(`已滚动到风险项: ${item.commentId}`);
          } else {
            console.warn(`未找到风险项DOM元素: ${item.commentId}`);
          }
        });
      },
      
      // 检查风险项是否被选中
      isItemSelected(item: DocData): boolean {
        return this.selectedItemId === item.commentId;
      },
      
      // 清除选中状态
      clearSelectedItem(): void {
        this.selectedItemId = '';
      },

      // 插入文本 - 获取风险项的 suggestion 并通过回调函数通知外部组件
      async insertText(item: DocData) {
        // 如果是只读模式，则不允许插入文本
        if (this.isReadOnly) {
          console.warn('当前为只读模式，无法插入文本');
          return false;
        }
        
        // 检查 suggestion 是否存在
        if (!item.suggestion || item.suggestion.trim() === '') {
          console.warn(`风险项 ${item.commentId} 没有可用的提示文本`);
          return false;
        }
        
        console.log(`插入文本: ${item.suggestion}`);
        
        // 调用外部提供的插入文本回调函数
        if (dataManager.callbacks.onTextInsert) {
          try {
            const result = await dataManager.callbacks.onTextInsert(item.suggestion, item);
            if (result) {
              // 回调成功后发送自定义消息
              this.$dispatch('text-insert', {
                suggestion: item.suggestion,
                item: item 
              });
              console.log(`已成功插入文本并发送消息: ${item.suggestion}`);
              return true;
            } else {
              console.warn(`插入文本失败: ${item.suggestion}`);
              return false;
            }
          } catch (error) {
            console.error('插入文本时发生错误:', error);
            return false;
          }
        } else {
          // 如果没有提供回调函数，直接发送自定义消息
          this.$dispatch('text-insert', {
            suggestion: item.suggestion,
            item: item 
          });
          console.log(`已直接发送文本插入消息: ${item.suggestion}`);
          return true;
        }
      },
      
      /**
       * 切换风险项备注内容的显示/隐藏状态
       * @param itemOrId 风险项对象或者 commentId
       * @returns 切换后的状态（true 表示显示，false 表示隐藏）
       */
      toggleCommentContent(itemOrId: DocData | string): boolean {
        const commentId = typeof itemOrId === 'string' ? itemOrId : itemOrId.commentId;
        const item = typeof itemOrId === 'string' ? this.findItemByCommentId(commentId) : itemOrId;

        // 如果传入的是风险项对象，确保 itemNote 存在
        if (typeof itemOrId !== 'string' && !itemOrId.itemNote) {
          // 初始化 itemNote 对象
          itemOrId.itemNote = { content: '', status: 0 };
          console.log(`为风险项 ${commentId} 初始化 itemNote 对象`);
        }

        // 获取当前状态
        let currentState;
        if (this.expandedComments.hasOwnProperty(commentId)) {
          // 如果已经有明确状态，使用该状态
          currentState = this.expandedComments[commentId];
        } else {
          // 如果没有明确状态，根据内容决定当前状态
          currentState = !!(item && item.itemNote && item.itemNote.content && item.itemNote.content.trim());
        }

        // 切换状态 - 使用对象展开语法触发 Alpine.js 响应式更新
        const newState = !currentState;
        this.expandedComments = {
          ...this.expandedComments,
          [commentId]: newState
        };

        console.log(`${newState ? '显示' : '隐藏'}风险项备注内容: ${commentId}`);
        console.log('toggleCommentContent 调试信息:', {
          commentId,
          currentState,
          newState,
          expandedComments: { ...this.expandedComments },
          itemNote: item?.itemNote,
          expandedCommentsKeys: Object.keys(this.expandedComments)
        });

        // 强制触发 Alpine.js 响应式更新
        this.commentVisibilityTrigger = Date.now();
        console.log('更新 commentVisibilityTrigger:', this.commentVisibilityTrigger);

        return newState;
      },
      
      /**
       * 检查风险项备注内容是否可见
       * @param itemOrId 风险项对象或者 commentId
       * @returns 是否可见
       */
      isCommentContentVisible(itemOrId: DocData | string): boolean {
        const commentId = typeof itemOrId === 'string' ? itemOrId : itemOrId.commentId;
        const item = typeof itemOrId === 'string' ? this.findItemByCommentId(commentId) : itemOrId;

        // 使用触发器强制重新计算（触发器变化时此函数会重新执行）
        void this.commentVisibilityTrigger;

        console.log('isCommentContentVisible 调试信息 - 开始:', {
          commentId,
          hasExpandedState: this.expandedComments.hasOwnProperty(commentId),
          expandedValue: this.expandedComments[commentId],
          itemNote: item?.itemNote,
          expandedComments: { ...this.expandedComments },
          trigger: this.commentVisibilityTrigger
        });

        // 如果已经有明确的展开状态设置（用户点击过备注按钮），使用该状态
        if (this.expandedComments.hasOwnProperty(commentId)) {
          const result = !!this.expandedComments[commentId];
          console.log('isCommentContentVisible 调试信息 - 使用已有状态:', { commentId, result });
          return result;
        }

        // 如果没有明确设置状态，根据备注内容决定默认显示状态
        // 如果有内容，默认显示；如果没有内容，默认不显示
        if (item && item.itemNote && item.itemNote.content && item.itemNote.content.trim()) {
          // 有内容时，设置默认状态为显示，并记录到 expandedComments 中
          this.expandedComments = {
            ...this.expandedComments,
            [commentId]: true
          };
          console.log('isCommentContentVisible 调试信息 - 有内容，设置为显示:', { commentId });
          return true;
        }

        // 没有内容时，设置默认状态为不显示，并记录到 expandedComments 中
        this.expandedComments = {
          ...this.expandedComments,
          [commentId]: false
        };
        console.log('isCommentContentVisible 调试信息 - 无内容，设置为隐藏:', { commentId });
        return false;
      },

      /**
       * 根据 commentId 查找风险项
       * @param commentId 风险项的 commentId
       * @returns 找到的风险项对象，如果未找到则返回 undefined
       */
      findItemByCommentId(commentId: string): DocData | undefined {
        return this.allItems.find(item => item.commentId === commentId);
      },

      /**
       * 判断当前风险项是否显示订阅
       * @param item 风险项对象
       * @returns 是否显示订阅
       */
      isItemSubscribeVisible(item: DocData): boolean {
        const subscribeRoles = config.subscribeRoles;
        const role = item.role;
        
        if (subscribeRoles === "-1") {
          return false;
        } else {
          return role === "All" || subscribeRoles === "All" || (subscribeRoles.indexOf(role) !== -1);
        }
      },
      
      /**
       * 切换缺失条款容器的展开/折叠状态
       * @returns 切换后的状态
       */
      toggleMissingClauseContainer(): boolean {
        this.isMissingClauseContainerExpanded = !this.isMissingClauseContainerExpanded;
        
        // 如果当前容器被展开，则折叠另一个容器
        if (this.isMissingClauseContainerExpanded) {
          this.isRiskPromptContainerExpanded = false;
          console.log('展开缺失条款容器，同时折叠风险提示容器');
        } else {
          console.log('折叠缺失条款容器');
        }
        
        return this.isMissingClauseContainerExpanded;
      },
      
      /**
       * 切换风险提示容器的展开/折叠状态
       * @returns 切换后的状态
       */
      toggleRiskPromptContainer(): boolean {
        this.isRiskPromptContainerExpanded = !this.isRiskPromptContainerExpanded;
        
        // 如果当前容器被展开，则折叠另一个容器
        if (this.isRiskPromptContainerExpanded) {
          this.isMissingClauseContainerExpanded = false;
          console.log('展开风险提示容器，同时折叠缺失条款容器');
        } else {
          console.log('折叠风险提示容器');
        }
        
        return this.isRiskPromptContainerExpanded;
      },
      
      /**
       * 确保风险项所在的容器是展开的
       * @param item 风险项对象
       * @returns 是否成功确保容器展开
       */
      ensureContainerExpanded(item: DocData): boolean {
        if (!item.type) {
          console.warn(`风险项 ${item.commentId} 没有类型信息`);
          return false;
        }
        
        if (item.type === 2 && !this.isMissingClauseContainerExpanded) {
          console.log('缺失条款容器已折叠，正在展开...');
          this.isMissingClauseContainerExpanded = true;
          // 确保风险提示容器折叠，保持互斥关系
          this.isRiskPromptContainerExpanded = false;
          return true;
        } else if (item.type === 1 && !this.isRiskPromptContainerExpanded) {
          console.log('风险提示容器已折叠，正在展开...');
          this.isRiskPromptContainerExpanded = true;
          // 确保缺失条款容器折叠，保持互斥关系
          this.isMissingClauseContainerExpanded = false;
          return true;
        }
        
        return true; // 容器已经是展开状态
      },
      
      /**
       * 保存风险项
       * @param item 风险项对象
       * @returns 是否保存成功
       */
      async saveItem(item: DocData): Promise<boolean> {
        // 如果是只读模式，则不允许保存
        if (this.isReadOnly) {
          console.warn('当前为只读模式，无法保存风险项');
          return false;
        }
        
        // 检查 objectId 是否存在
        if (!item.objectId) {
          console.warn(`风险项 ${item.commentId} 没有 objectId，无法保存风险项`);
          return false;
        }

        console.log(`保存风险项: ${item.commentId}`);
        
        // 保存原始的 item 完整副本，用于在保存失败时还原
        const allItemIndex = this.allItems.findIndex(i => i.commentId === item.commentId);
        const originalItem = this.allItems[allItemIndex];
        
        // 调用外部提供的保存风险项回调函数
        if (dataManager.callbacks.onSaveItem) {
          try {
            const result = await dataManager.callbacks.onSaveItem(item);
            if (result) {
              // 保存成功，确保 allItems 中的数据与当前 item 一致
              if (allItemIndex !== -1) {
                console.log(`更新 allItems 中的风险项数据: ${item.commentId}`);
                this.allItems[allItemIndex] = item;
              }
              
              // 同时更新 RiskItemsManager 的内部数据存储
              dataManager.updateItem(item);
              
              // 回调成功后发送自定义消息
              this.$dispatch('item-save', {
                objectId: item.objectId,
                type: item.type,
                item: item
              });
              console.log(`已成功保存风险项并发送消息，objectId: ${item.objectId}`);
              return true;
            } else {
              // 保存失败，还原当前 item 的内容
              console.log(`保存风险项失败，还原当前项原始内容: ${item.commentId}`);
              // 还原 filteredItems 中的项
             this.restoreItem(item.commentId, originalItem);
              
              console.warn(`保存风险项失败: ${item.commentId}`);
              return false;
            }
          } catch (error) {
            // 发生错误，还原当前 item 的内容
            Object.assign(item, originalItem);
            console.log(`保存风险项出错，还原当前项原始内容: ${item.commentId}`);
            
            // 还原 allItems 和 filteredItems 中对应的 item
            this.restoreItem(item.commentId, originalItem);
            
            console.error('保存风险项时发生错误:', error);
            return false;
          }
        } else {
          // 如果没有提供回调函数，直接发送自定义消息
          this.$dispatch('item-save', {
            objectId: item.objectId,
            type: item.type,
            item: item
          });
          console.log(`已直接发送保存风险项消息，objectId: ${item.objectId}`);
          return true;
        }
      },
      
      /**
       * 在 allItems 和 filteredItems 集合中还原指定 commentId 的风险项
       * @param commentId 要还原的风险项 ID
       * @param originalItem 原始的风险项数据
       */
      restoreItem(commentId: string, originalItem: DocData): void { 
        // 还原 filteredItems 中的项
        const filteredItemIndex = this.filteredItems.findIndex(i => i.commentId === commentId);
        if (filteredItemIndex !== -1) {
          this.filteredItems[filteredItemIndex] = originalItem;
          console.log(`已还原 filteredItems 中风险项的原始内容: ${commentId}`);
        }
      }
    }));
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    if (this.container) {
      this.container.innerHTML = '';
    }
    this.isInitialized = false;
    console.log('RiskItemsManager 已销毁');
  }

  /**
   * 检查是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 通过 commentId 查找风险项并滚动到卡片处
   * @param commentId 备注ID
   * @returns 是否成功找到并滚动
   */
  public scrollToItemByCommentId(commentId: string): boolean {
    if (!this.isInitialized) {
      console.error('RiskItemsManager 未初始化，无法滚动到风险项');
      return false;
    }

    const item = this.data.find(item => item.commentId === commentId);
    if (!item) {
      console.warn(`未找到 commentId 为 ${commentId} 的风险项`);
      return false;
    }

    try {
      // 直接执行滚动逻辑
      this.directScrollToItem(item);
      console.log(`正在滚动到 commentId 为 ${commentId} 的风险项`);
      return true;
    } catch (error) {
      console.error(`滚动到风险项时发生错误:`, error);
      return false;
    }
  }

  /**
   * 通过 objectId 查找风险项并滚动到卡片处
   * @param objectId 对象ID
   * @returns 是否成功找到并滚动
   */
  public scrollToItemByObjectId(objectId: string): boolean {
    if (!this.isInitialized) {
      console.error('RiskItemsManager 未初始化，无法滚动到风险项');
      return false;
    }

    const items = this.data.filter(item => item.objectId === objectId);
    if (items.length === 0) {
      console.warn(`未找到 objectId 为 ${objectId} 的风险项`);
      return false;
    }

    try {
      // 默认滚动到第一个匹配的项
      const item = items[0];
      
      // 直接执行滚动逻辑
      this.directScrollToItem(item);
      console.log(`正在滚动到 objectId 为 ${objectId} 的风险项，共 ${items.length} 项`);
      return true;
    } catch (error) {
      console.error(`滚动到风险项时发生错误:`, error);
      return false;
    }
  }

  /**
   * 直接执行滚动到指定风险项的操作
   * @param item 风险项数据
   */
  private directScrollToItem(item: DocData): void {
    // 获取 Alpine.js 组件的实例
    // 先尝试获取绑定在 body 上的 Alpine.js 组件实例
    let alpineComponent = null;
    
    try {
      // 尝试从 body 元素获取 Alpine 实例（适应新版 Alpine.js 结构）
      const bodyElement = document.body as any;
      if (bodyElement._x_dataStack && bodyElement._x_dataStack.length > 0) {
        // 获取最新的数据栈
        alpineComponent = bodyElement._x_dataStack[bodyElement._x_dataStack.length - 1];
        console.log('从 body 元素的 _x_dataStack 获取到 Alpine.js 组件实例');
      } 
      // 如果 body 上没有，再尝试通过选择器查找
      else {
        const alpineElement = document.querySelector('[x-data="riskItemsData"]') as any;
        if (alpineElement && alpineElement._x_dataStack && alpineElement._x_dataStack.length > 0) {
          alpineComponent = alpineElement._x_dataStack[alpineElement._x_dataStack.length - 1];
          console.log('从选择器查找到 Alpine.js 组件实例');
        }
      }
      
      if (!alpineComponent) {
        // 尝试通过 Alpine.js 的全局访问
        if (window.Alpine && (window.Alpine as any).closestDataStack) {
          const dataStack = (window.Alpine as any).closestDataStack(document.body);
          if (dataStack && dataStack.length > 0) {
            alpineComponent = dataStack[dataStack.length - 1];
            console.log('从 Alpine.closestDataStack 获取到组件实例');
          }
        }
      }
    } catch (error) {
      console.error('尝试获取 Alpine.js 组件实例时出错:', error);
    }
    
    if (!alpineComponent) {
      console.error('无法获取 Alpine.js 组件实例，请确保 x-data="riskItemsData" 已正确绑定');
      return;
    }
    
    // 确保风险项所在的容器是展开的
    if (item.type && typeof alpineComponent.ensureContainerExpanded === 'function') {
      alpineComponent.ensureContainerExpanded(item);
    } else {
      console.warn('无法确保容器展开，找不到 ensureContainerExpanded 方法');
    }
    
    // 等待DOM更新后再执行滚动
    setTimeout(() => {
      // 尝试直接查找并滚动到元素
      const itemElement = document.querySelector(`.retmenus[data-comment-id="${item.commentId}"]`);
      if (itemElement) {
        // 滚动到元素位置
        itemElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 设置选中的风险项ID
        if (typeof alpineComponent.selectedItemId !== 'undefined') {
          alpineComponent.selectedItemId = item.commentId;
          console.log(`已设置选中的风险项: ${item.commentId}`);
        } else {
          console.warn('无法设置选中的风险项，找不到 selectedItemId 属性');
        }
        
        console.log(`已滚动到风险项: ${item.commentId}`);
      } else {
        console.warn(`未找到风险项DOM元素: ${item.commentId}，请检查HTML模板中的data-comment-id属性`);
      }
    }, 300); // 给足够的时间让DOM更新
  }
  
  /**
   * 清除当前选中的风险项
   */
  public clearSelectedItem(): void {
    let alpineComponent = null;
    
    try {
      // 尝试从 body 元素获取 Alpine 实例（适应新版 Alpine.js 结构）
      const bodyElement = document.body as any;
      if (bodyElement._x_dataStack && bodyElement._x_dataStack.length > 0) {
        // 获取最新的数据栈
        alpineComponent = bodyElement._x_dataStack[bodyElement._x_dataStack.length - 1];
      } 
      // 如果 body 上没有，再尝试通过选择器查找
      else {
        const alpineElement = document.querySelector('[x-data="riskItemsData"]') as any;
        if (alpineElement && alpineElement._x_dataStack && alpineElement._x_dataStack.length > 0) {
          alpineComponent = alpineElement._x_dataStack[alpineElement._x_dataStack.length - 1];
        }
      }
      
      if (!alpineComponent) {
        // 尝试通过 Alpine.js 的全局访问
        if (window.Alpine && window.Alpine.closestDataStack) {
          const dataStack = window.Alpine.closestDataStack(document.body);
          if (dataStack && dataStack.length > 0) {
            alpineComponent = dataStack[dataStack.length - 1];
          }
        }
      }
    } catch (error) {
      console.error('尝试获取 Alpine.js 组件实例时出错:', error);
    }
    
    if (alpineComponent && typeof alpineComponent.selectedItemId !== 'undefined') {
      alpineComponent.selectedItemId = '';
      console.log('已清除选中的风险项');
    } else {
      console.warn('无法获取 Alpine.js 组件实例或找不到 selectedItemId 属性，无法清除选中的风险项');
    }
  }
}

export default RiskItemsManager;