/**
 * utils.ts
 * 项目通用工具类
 */

/**
 * 判断字符串是否为有效的JSON格式且不为空
 * @param str - 要检验的字符串
 * @returns 如果是有效的JSON字符串且不为空则返回true，否则返回false
 */
export function isValidJson(str: string | null | undefined): boolean {
  // 检查输入是否为空
  if (!str || typeof str !== 'string' || str.trim() === '') {
    return false;
  }

  try {
    // 尝试解析JSON
    const parsed = JSON.parse(str);
    
    // 检查解析结果是否为null或undefined
    if (parsed === null || parsed === undefined) {
      return false;
    }
    
    // 如果是对象或数组，检查是否为空
    if (typeof parsed === 'object') {
      if (Array.isArray(parsed)) {
        // 数组不为空则认为有效
        return parsed.length > 0;
      } else {
        // 对象不为空则认为有效
        return Object.keys(parsed).length > 0;
      }
    }
    
    // 基本类型（string, number, boolean）都认为有效
    return true;
  } catch (error) {
    // JSON解析失败
    return false;
  }
}

/**
 * 安全地解析JSON字符串
 * @param str - 要解析的JSON字符串
 * @param defaultValue - 解析失败时返回的默认值
 * @returns 解析后的对象或默认值
 */
export function safeJsonParse<T = any>(str: string | null | undefined, defaultValue: T): T {
  if (!isValidJson(str)) {
    return defaultValue;
  }
  
  try {
    return JSON.parse(str as string);
  } catch (error) {
    console.warn('JSON解析失败:', error);
    return defaultValue;
  }
}

/**
 * 安全地将对象转换为JSON字符串
 * @param obj - 要转换的对象
 * @param space - 格式化空格数，默认为0（压缩格式）
 * @returns JSON字符串或空字符串（转换失败时）
 */
export function safeJsonStringify(obj: any, space: number = 0): string {
  try {
    return JSON.stringify(obj, null, space);
  } catch (error) {
    console.warn('JSON序列化失败:', error);
    return '';
  }
}

/**
 * 检查字符串是否为空或只包含空白字符
 * @param str - 要检查的字符串
 * @returns 如果为空或只包含空白字符则返回true
 */
export function isEmpty(str: string | null | undefined): boolean {
  return !str || str.trim() === '';
}

/**
 * 检查对象是否为空
 * @param obj - 要检查的对象
 * @returns 如果对象为空则返回true
 */
export function isEmptyObject(obj: any): boolean {
  if (!obj || typeof obj !== 'object') {
    return true;
  }
  
  if (Array.isArray(obj)) {
    return obj.length === 0;
  }
  
  return Object.keys(obj).length === 0;
}

/**
 * 生成唯一ID
 * @param prefix - ID前缀，默认为空
 * @returns 唯一ID字符串
 */
export function generateUniqueId(prefix: string = ''): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2);
  return `${prefix}${timestamp}${randomPart}`;
}

/**
 * 防抖函数
 * @param func - 要执行的函数
 * @param delay - 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      func.apply(null, args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param func - 要执行的函数
 * @param limit - 时间间隔（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
} 