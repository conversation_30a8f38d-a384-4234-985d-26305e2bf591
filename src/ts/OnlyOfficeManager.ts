/**
 * OnlyOfficeManager.ts
 * 用于管理 OnlyOffice 文档编辑器的类，主要用于合同审核前台展示功能
 */

import AppMessage from './AppMessage';
import type { CommentData, OnlyOfficeConfig, OnlyOfficeInstance, DocData, OnlyOfficeManagerConfig } from './types/onlyoffice';
import { isValidJson, safeJsonParse, throttle } from './utils';

/**
 * OnlyOffice 管理器类
 * 用于初始化和管理 OnlyOffice 文档编辑器，处理合同审核相关功能
 */
export class OnlyOfficeManager {
  private  static readonly COLOR_SHOW:number[]=[248, 184, 91, .3];
  private  static readonly COLOR_ACTIVE:number[]= [196, 29, 127, .3];
  private container: HTMLElement | null = null;
  private instance: OnlyOfficeInstance | null = null;
  private config: OnlyOfficeConfig;
  private isInitialized: boolean = false;
  private contractId: string | null = null;
  private messageManager: AppMessage | null = null;
  private isSdkReady: boolean = false;
  private isDocumentReady: boolean = false;
  private _docData: DocData[] = [];
  
  // 新增配置属性
  private managerConfig: OnlyOfficeManagerConfig;
  private isShowComment: boolean;//是否显示备注
  private isDevelopment: boolean;//是否为开发环境

  // 备注跳转相关属性
  private domObjectArrayIndex: number = 0; // 点击次数数组下标
  private currentCommentObjectId: string | undefined = undefined; // 当前备注的objectId

  /**
   * 构造函数
   * @param managerConfig - OnlyOffice 管理器配置
   */
  constructor(managerConfig: OnlyOfficeManagerConfig) {
    this.managerConfig = managerConfig;
    this.container = document.getElementById(managerConfig.containerId);
    this.config = managerConfig.config;
    
    // 初始化配置属性
    this.isShowComment = managerConfig.isShowComment;
    this.isDevelopment = managerConfig.isDevelopment;
    
    // 初始化消息管理器
    this.initMessageManager();
  }

  /**
   * 初始化消息管理器
   */
  private initMessageManager(): void {
    this.messageManager = new AppMessage();
  }

  

  /**
   * 初始化 OnlyOffice 编辑器
   * @returns 是否初始化成功
   */
  public init(): boolean {
    if (!this.container) {
      console.error('Container element not found');
      return false;
    }

    // 绑定事件处理函数 - 保留原有事件并添加我们的监听
    if (!this.config.events) {
      this.config.events = {};
    }
    
    // 保存原有的事件处理函数
    const originalEvents = { ...this.config.events };
    
    // 创建组合事件处理函数
    this.config.events.onAppReady = (event: any) => {
      if (originalEvents.onAppReady) {
        originalEvents.onAppReady(event);
      }
      this.handleAppReady(event);
    };
    
    this.config.events.onDocumentStateChange = (event: any) => {
      if (originalEvents.onDocumentStateChange) {
        originalEvents.onDocumentStateChange(event);
      }
      this.handleDocumentStateChange(event);
    };
    
    this.config.events.onRequestSaveAs = (event: any) => {
      if (originalEvents.onRequestSaveAs) {
        originalEvents.onRequestSaveAs(event);
      }
      this.handleRequestSaveAs(event);
    };
    
    this.config.events.onRequestClose = () => {
      if (originalEvents.onRequestClose) {
        originalEvents.onRequestClose();
      }
      this.handleRequestClose();
    };
    
    this.config.events.onDocumentReady = (event: any) => {
      if (originalEvents.onDocumentReady) {
        originalEvents.onDocumentReady(event);
      }
      this.handleDocumentReady(event);
    };
    
    this.config.events.onInfo = (event: any) => {
      if (originalEvents.onInfo) {
        originalEvents.onInfo(event);
      }
      this.handleInfo(event);
    };

    // 添加编辑器完全准备就绪事件
    this.config.events.onEditorReady = (event: any) => {
      if (originalEvents.onEditorReady) {
        originalEvents.onEditorReady(event);
      }
      // 这个事件会在 checkAndLoadAllComments 中被调用
    };

    try {
      // 初始化编辑器
      this.fetchContext7Library().then(() => {
        if (!window.DocsAPI) {
          console.error('OnlyOffice API 未加载成功');
          return;
        }
        // 根据 OnlyOffice 官方文档，DocEditor 构造函数的第一个参数应该是容器的 ID 字符串
        // 使用类型断言来解决类型不匹配的问题
        this.instance = new window.DocsAPI.DocEditor(this.managerConfig.containerId , this.config);
        this.isInitialized = true;
        console.log('OnlyOffice editor initialized successfully');
      }).catch(error => {
        console.error('Failed to load OnlyOffice library:', error);
      });
      
      return true;
    } catch (error) {
      console.error('Failed to initialize OnlyOffice editor:', error);
      return false;
    }
  }

  /**
   * 检查 OnlyOffice 库是否加载完毕
   * @returns Promise
   */
  private fetchContext7Library(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果已经加载完毕，直接返回
      if (window.DocsAPI) {
        resolve();
        return;
      }
      
      // 等待库加载完成的轮询检查
      let attempts = 0;
      const maxAttempts = 50; // 最多等待5秒 (50 * 100ms)
      
      const checkLibrary = () => {
        attempts++;
        
        if (window.DocsAPI) {
          console.log('OnlyOffice API 已准备就绪');
          resolve();
          return;
        }
        
        if (attempts >= maxAttempts) {
          console.error('OnlyOffice API 加载超时，请确保页面已正确引入OnlyOffice库');
          reject(new Error('OnlyOffice API 加载超时'));
          return;
        }
        
        // 100ms后再次检查
        setTimeout(checkLibrary, 100);
      };
      
      // 开始检查
      checkLibrary();
    });
  }

  /**
   * 销毁编辑器实例
   */
  public destroy(): void {
    if (this.isInitialized && this.instance) {
      try {
        this.instance.destroyEditor();
        this.isInitialized = false;
        this.instance = null;
        console.log('OnlyOffice editor destroyed');
      } catch (error) {
        console.error('Failed to destroy OnlyOffice editor:', error);
      }
    }
  }

  

  


  /**
   * 应用程序就绪事件处理
   * @param event - 事件对象
   */
  private handleAppReady(event: any): void {
    console.log('OnlyOffice app is ready', event);
    // 可以在这里执行初始化后的操作
  }

  /**
   * 文档状态变更事件处理
   * @param event - 事件对象
   */
  private handleDocumentStateChange(event: any): void {
    console.log('Document state changed', event);
    // 可以在这里处理文档状态变化
  }

  /**
   * 请求另存为事件处理
   * @param event - 事件对象
   */
  private handleRequestSaveAs(event: any): void {
    console.log('Save as requested', event);
    // 处理另存为请求
  }

  /**
   * 请求关闭事件处理
   */
  private handleRequestClose(): void {
    console.log('Close requested');
    // 处理关闭请求，例如提示保存等
  }

  /**
   * 获取当前合同 ID
   * @returns 合同 ID
   */
  public getContractId(): string | null {
    return this.contractId;
  }

  /**
   * 检查编辑器是否初始化
   * @returns 是否已初始化
   */
  public isEditorInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 执行编辑器脚本
   * @param scriptText - 要执行的脚本文本
   * @param data - 要传递的附加数据
   * @returns 脚本执行结果的Promise
   */
  private executeEditorScript(scriptText: string, data: any = null): Promise<any> {
    if (!this.messageManager) {
      console.error('Message manager not initialized');
      return Promise.reject(new Error('Message manager not initialized'));
    }

    if (!this.isInitialized) {
      console.error('Editor not initialized');
      return Promise.reject(new Error('Editor not initialized'));
    }
    return this.messageManager.executeScript(scriptText.trim(), data);
  }

 

  /**
     * 获取所有自定义备注数据
     * @private
     */
  public async getCommentDatas(){
      return await this.executeEditorScript("return DaoRigin.getAllComments({ quoteText: true })");
  }

  /**
   * 文档就绪事件处理
   * 当文档完全加载并准备好进行编辑时触发
   * @param event - 事件对象
   */
  private handleDocumentReady(event: any): void {
    console.log('OnlyOffice 文档已加载完成并准备就绪', event);
    
    // 设置文档就绪状态
    this.isDocumentReady = true;
 
    // 检查两个条件是否都满足，如果是则获取所有备注
    this.checkAndLoadAllComments();
  }

  /**
   * 信息事件处理
   * 当编辑器需要显示某些信息给用户时触发
   * @param event - 信息事件对象
   */
  private handleInfo(event: any): void {
    console.log('OnlyOffice 编辑器信息:', event);
    
    // 处理不同类型的信息事件
    if (event && event.data) {
      // 根据事件类型执行不同操作
      switch (event.data.type) {
        case 'action':
          // 处理用户操作信息
          console.log('用户操作:', event.data.action);
          break;
        case 'error':
          // 处理错误信息
          console.error('编辑器错误:', event.data.message);
          break;
        case 'warning':
          // 处理警告信息
          console.warn('编辑器警告:', event.data.message);
          break;
        case 'info':
          // 处理普通信息
          console.info('编辑器信息:', event.data.message);
          break;
        case 'onCommentClick':
          // 处理备注点击事件
          console.log('备注点击事件:', event.data.commentId);
          // 处理备注点击事件
          if (event.data.data && Array.isArray(event.data.data)) {
            this.handleCommentClick(event.data.data);
          }
          break;
        case 'onSdkReady':
          // 处理 SDK 就绪事件
          console.log('SDK 就绪事件:', event.data.message);
          // 设置SDK就绪状态
          this.isSdkReady = true;
          // 检查两个条件是否都满足，如果是则获取所有备注
          this.checkAndLoadAllComments();
          break;
        default:
          // 处理其他类型信息
          console.log('其他编辑器信息:', event.data);
      }
    }
  }
  
  /**
   * 检查SDK和文档是否都就绪，如果是则获取所有备注
   * @private
   */
  private async checkAndLoadAllComments(): Promise<void>  {
    if (this.isSdkReady && this.isDocumentReady) {
      console.log('SDK和文档都已就绪，开始获取所有备注');
      if(this.isShowComment){
        await this.getAllComments();
        await this.updateAllCommentsStyle();
      }

      // 调用编辑器完全准备就绪的回调函数
      if (this.config.events && this.config.events.onEditorReady) {
        console.log('调用 onEditorReady 回调函数');
        try {
          this.config.events.onEditorReady({
            type: 'editorReady',
            message: 'OnlyOffice editor is fully ready',
            timestamp: new Date().toISOString(),
            sdkReady: this.isSdkReady,
            documentReady: this.isDocumentReady,
            commentsLoaded: this.isShowComment
          });
        } catch (error) {
          console.error('调用 onEditorReady 回调函数时发生错误:', error);
        }
      }
    } else {
      console.log('等待SDK和文档都就绪，当前状态：', {
        sdkReady: this.isSdkReady,
        documentReady: this.isDocumentReady
      });
    }
  }
  
  /**
   * 处理备注点击事件（节流版本）
   * 使用节流函数控制点击频率，避免频繁触发
   * @param commentIds - 备注ID数组
   */
  private handleCommentClick = throttle((commentIds: string[]): void => {
    console.log('处理备注点击事件，备注IDs:', commentIds);
    
    if (!commentIds || commentIds.length === 0) {
      console.warn('备注ID列表为空');
      return;
    }
    
    try {
      // 获取第一个备注ID
      const firstCommentId = commentIds[0];
      const firstCommentObjectId = this.getObjectIdByCommentId(firstCommentId);
      
      // 触发自定义事件，通知外部组件
      const customEvent = new CustomEvent('onlyoffice-comment-clicked', {
        detail: {
          firstCommentObjectId,
          timestamp: Date.now()
        }
      });
      document.dispatchEvent(customEvent);
      
    } catch (error) {
      console.error('处理备注点击事件时发生错误:', error);
    }
  }, 300); // 300ms 节流间隔
  

  /**
   * 通过备注ID查找对应的对象ID
   * @param commentId - 备注ID
   * @returns 对象ID，如果未找到则返回undefined
   * @private
   */
  private getObjectIdByCommentId(commentId: string): string | undefined {
    const docData = this._docData.find(data => data.commentId === commentId);
    return docData ? docData.objectId : undefined;
  }

  /**
   * 从数据结构中删除备注的公用方法
   * 同时清理_docData中的数据
   * @param commentIds 要删除的备注ID数组
   * @returns 被影响的objectIds集合
   * @private
   */
  private removeCommentsFromDataStructures(commentIds: string[]): Set<string> {
    const affectedObjectIds = new Set<string>();
    
    // 从_docData中删除备注数据，并收集相关的objectIds
    commentIds.forEach(commentId => {
      const docDataIndex = this._docData.findIndex(data => data.commentId === commentId);
      if (docDataIndex !== -1) {
        const docData = this._docData[docDataIndex];
        
        // 记录受影响的objectId
        if (docData.objectId) {
          affectedObjectIds.add(docData.objectId);
        }
        
        // 从_docData中删除
        this._docData.splice(docDataIndex, 1);
        console.log(`已从数据中删除备注: ${commentId}`);
      } else {
        console.warn(`未找到备注ID为 ${commentId} 的数据`);
      }
    });

    return affectedObjectIds;
  }

  /**
   * 解析备注数据
   * @param commentDatas - 备注数据数组
   */
  private setCommentDatas(commentDatas: CommentData[]): void {
    // 清空之前的数据
    this._docData = [];
    
    commentDatas.forEach((commentData: CommentData): void => {
      const { id: commentId, initials, quoteText } = commentData;
      let isJson = isValidJson(initials);
      if (isJson) {
        const { role,indexID, level, hint, partyType, adoptSuggestion, name, knowledgeSource, suggestion = "", typeName, type, objectId, relationLaws, relationCase,itemNote } = safeJsonParse(initials,{} as any);
        let _docData: DocData = {
          commentId: commentId,
          role: role,
          level: level,
          hint: hint,
          partyType: partyType,
          adoptSuggestion: adoptSuggestion,
          indexID: indexID || 0,
          name: name,
          knowledgeSource: knowledgeSource,
          suggestion: suggestion,
          quoteText: quoteText,
          type: type,
          objectId: objectId,
          relationLaws: relationLaws ? JSON.parse(relationLaws) : [],
          relationCase: relationCase ? JSON.parse(relationCase) : null,
          typeName: typeName,
          itemNote: itemNote || { content: "", status: -1 },
        };
        this._docData.push(_docData);
      }
    });
    
    console.log('解析备注数据完成，共解析:', this._docData.length, '条数据');
  }


  /**
   * 获取解析后的文档数据
   * @returns 文档数据数组
   */
  public getDocData(): DocData[] {
    return this._docData;
  }

  /**
   * 根据objectId获取对应的文档数据
   * 使用直接过滤的方式，不依赖映射表
   * @param objectId - 对象ID
   * @returns 对应的文档数据数组
   */
  public getDocDataByObjectId(objectId: string): DocData[] {
    if (!objectId) {
      return [];
    }
    return this._docData.filter(data => data.objectId === objectId);
  }

  /**
   * 获取文档中的所有备注（重写以包含解析逻辑）
   * @returns 所有备注的Promise
   */
  public async getAllComments(): Promise<CommentData[]> {
    try {
      console.log('正在获取所有备注...');
      const comments: CommentData[] = await this.getCommentDatas();
      console.log('获取备注成功:', comments);
      
      // 解析备注数据
      this.setCommentDatas(comments);

      // 触发事件通知备注加载完成
      const customEvent = new CustomEvent('onlyoffice-comments-loaded', { 
        detail: { 
          comments,
          docData: this._docData
        } 
      });
      document.dispatchEvent(customEvent);
      
      return comments;
    } catch (error) {
      console.error('获取备注失败:', error);
      return [];
    }
  }

  /**
   * 获取管理器配置
   * @returns 管理器配置
   */
  public getManagerConfig(): OnlyOfficeManagerConfig {
    return this.managerConfig;
  }

  /**
   * 获取是否显示备注
   * @returns 是否显示备注
   */
  public getIsShowComment(): boolean {
    return this.isShowComment;
  }

  /**
   * 获取是否为开发环境
   * @returns 是否为开发环境
   */
  public getIsDevelopment(): boolean {
    return this.isDevelopment;
  }

 


  /**
   * 更新所有备注的显示样式和颜色设置
   * 遍历所有备注，对于包含有效JSON数据的备注应用样式配置
   * @returns 执行结果的Promise
   * @private
   */
  private async updateAllCommentsStyle(): Promise<any> {
    const script: string = "\n" +
      "var commentList = DaoRigin.getAllComments(); \n" +
      "DaoRigin.handleAction(function() { \n" +
      "  commentList.forEach(function(commentDate){ \n" +
      "  var isJson=DaoRigin.isJSON(commentDate.initials);\n" +
      "  if (isJson) { \n" +
      "     var data ={hidePopover:true," +"         color:["+OnlyOfficeManager.COLOR_SHOW+"]," +
      "         activeColor:[" + OnlyOfficeManager.COLOR_ACTIVE + "]," +
      "      }; \n" +
      "      DaoRigin.updateComment(commentDate.id,data); \n" +
      "      DaoRigin.showComment(commentDate.id);\n" +
      " } \n" +
      "})\n" +
      "})\n";
    
    try {
      console.log('开始更新所有备注样式...');
      const result = await this.executeEditorScript(script);
      console.log('备注样式更新完成:', result);
      return result;
    } catch (error) {
      console.error('更新备注样式失败:', error);
      throw error;
    }
  }

  /**
   * 
   * 对外公用函数
   */

   /**
   * 滚动到备注位置
   * @param commentId 备注ID
   */
   public async scrollToComment(commentId:string){
        const script:string="\n let range = DaoRigin.getCommentRange(commentId)\n        if (range) {\n          DaoRigin.scrollToComment(commentId)\n          DaoRigin.getCommentRange(commentId).Select()\n        }\n        return !!range\n      ";
        this.executeEditorScript(script, {
            commentId: commentId
        })
    }

    /**
     * 通过右边菜单点击发送消息过来的objectId跳转到指定备注上
     * 支持同一objectId下多个备注的循环跳转
     * @param objectId 文档的objectId
     * @returns Promise<boolean> 成功返回true，失败返回false
     */
    public async scrollToCommentByObjectId(objectId: string): Promise<boolean> {
        if (!objectId) {
            console.warn('ObjectId为空，无法跳转到备注');
            return false;
        }

        console.log(`开始跳转到objectId为 ${objectId} 的备注`);

        try {
            // 检查是否为新的objectId，如果是则重置索引
            if (objectId !== this.currentCommentObjectId) {
                this.domObjectArrayIndex = 0;
                this.currentCommentObjectId = objectId;
                console.log(`切换到新的objectId: ${objectId}，重置索引为0`);
            }

            // 直接从_docData中过滤出匹配objectId的所有备注数据
            const docDataList: DocData[] = this._docData.filter(data => data.objectId === objectId);
            
            if (docDataList.length === 0) {
                console.warn(`未找到objectId为 ${objectId} 的备注数据`);
                return false;
            }

            console.log(`找到${docDataList.length}个相关备注，当前索引: ${this.domObjectArrayIndex}`);

            // 确保索引在有效范围内
            if (this.domObjectArrayIndex >= docDataList.length) {
                this.domObjectArrayIndex = 0;
                console.log(`索引超出范围，重置为0`);
            }

            // 获取当前索引对应的备注数据
            const currentDocData: DocData = docDataList[this.domObjectArrayIndex];
            if (!currentDocData || !currentDocData.commentId) {
                console.error(`索引 ${this.domObjectArrayIndex} 对应的备注数据无效`);
                return false;
            }

            const commentId: string = currentDocData.commentId;
            console.log(`跳转到备注ID: ${commentId} (索引: ${this.domObjectArrayIndex})`);

            // 更新索引，为下次点击做准备（循环）
            this.domObjectArrayIndex++;
            if (this.domObjectArrayIndex >= docDataList.length) {
                this.domObjectArrayIndex = 0;
            }

            // 执行滚动到备注的操作
            await this.scrollToComment(commentId);

            // 触发跳转事件，通知外部组件
            const jumpEvent = new CustomEvent('onlyoffice-comment-jumped', {
                detail: {
                    objectId,
                    commentId,
                    currentIndex: this.domObjectArrayIndex - 1, // 显示实际跳转的索引
                    totalCount: docDataList.length,
                    currentComment: currentDocData, // 提供当前备注的完整数据
                    nextIndex: this.domObjectArrayIndex, // 下次将要跳转的索引
                    isLastComment: this.domObjectArrayIndex === 0, // 是否已循环到最后一个
                    timestamp: Date.now()
                }
            });
            document.dispatchEvent(jumpEvent);

            console.log(`成功跳转到备注 ${commentId}，下次索引将为: ${this.domObjectArrayIndex}`);
            return true;

        } catch (error) {
            console.error(`跳转到objectId为 ${objectId} 的备注时发生错误:`, error);
            return false;
        }
    }

    /**
     * 重置备注跳转状态
     * 清除当前的objectId和索引状态
     */
    public resetCommentJumpState(): void {
        this.domObjectArrayIndex = 0;
        this.currentCommentObjectId = undefined;
        console.log('备注跳转状态已重置');
    }

    /**
     * 获取当前备注跳转状态
     * @returns 当前跳转状态信息
     */
    public getCommentJumpState(): { objectId?: string; index: number } {
        return {
            objectId: this.currentCommentObjectId,
            index: this.domObjectArrayIndex
        };
    }

    /**
     * 获取指定objectId的所有备注信息
     * @param objectId 文档的objectId
     * @returns 匹配的备注数据数组
     */
    public getCommentsByObjectId(objectId: string): DocData[] {
        if (!objectId) {
            console.warn('ObjectId为空，无法获取备注');
            return [];
        }
        
        const matchingComments = this._docData.filter(data => data.objectId === objectId);
        console.log(`ObjectId ${objectId} 共找到 ${matchingComments.length} 个备注`);
        return matchingComments;
    }

    /**
     * 检查指定objectId是否有备注
     * @param objectId 文档的objectId
     * @returns 是否存在备注
     */
    public hasComments(objectId: string): boolean {
        if (!objectId) return false;
        return this._docData.some(data => data.objectId === objectId);
    }

    /**
     * 根据备注ID删除备注
     * 同时从数据和显示上删除备注
     * @param commentIds 备注ID数组
     */
    public async deleteCommentByCommentId(commentIds: string[]): Promise<void> {
        if (!commentIds || commentIds.length === 0) {
            console.warn('备注ID列表为空，无法删除');
            return;
        }

        console.log('开始删除备注:', commentIds);

        try {
            // 1. 从数据结构中删除备注数据
            this.removeCommentsFromDataStructures(commentIds);

            // 2. 从OnlyOffice显示中删除备注
            await this.deleteCommentsFromDisplay(commentIds);

            // 3. 触发删除完成事件
            const deleteEvent = new CustomEvent('onlyoffice-comments-deleted', {
                detail: {
                    deletedCommentIds: commentIds,
                    timestamp: Date.now()
                }
            });
            document.dispatchEvent(deleteEvent);

            console.log('备注删除完成:', commentIds);

        } catch (error) {
            console.error('删除备注时发生错误:', error);
            throw error;
        }
    }
    
    /**
     * 根据对象ID删除所有相关备注
     * 同时从数据和显示上删除备注
     * @param objectId 对象ID
     */
    public async deleteCommentByObjectId(objectId: string): Promise<void> {
        if (!objectId) {
            console.warn('对象ID为空，无法删除');
            return;
        }

        console.log('开始根据对象ID删除备注:', objectId);

        try {
            // 1. 直接从_docData中过滤出匹配objectId的所有备注数据
            const relatedDocData = this._docData.filter(data => data.objectId === objectId);
            if (relatedDocData.length === 0) {
                console.warn(`未找到对象ID为 ${objectId} 的备注数据`);
                return;
            }

            // 2. 收集所有相关的commentId
            const commentIds = relatedDocData.map(data => data.commentId);
            console.log(`找到${commentIds.length}个相关备注:`, commentIds);

            // 3. 从数据结构中删除所有相关备注数据
            this.removeCommentsFromDataStructures(commentIds);

            // 4. 从OnlyOffice显示中删除备注
            await this.deleteCommentsFromDisplay(commentIds);

            // 5. 触发删除完成事件
            const deleteEvent = new CustomEvent('onlyoffice-comments-deleted', {
                detail: {
                    deletedCommentIds: commentIds,
                    deletedObjectId: objectId,
                    timestamp: Date.now()
                }
            });
            document.dispatchEvent(deleteEvent);

            console.log('根据对象ID删除备注完成:', objectId);

        } catch (error) {
            console.error('根据对象ID删除备注时发生错误:', error);
            throw error;
        }
    }

    /**
     * 从OnlyOffice显示中删除备注的私有方法
     * @param commentIds 要删除的备注ID数组
     * @private
     */
    private async deleteCommentsFromDisplay(commentIds: string[]): Promise<any> {
        if (!commentIds || commentIds.length === 0) {
            return;
        }

        const script: string = "\n" +
            "DaoRigin.handleAction(function() {\n" +
            "  commentList.forEach(function(commentId) {\n" +
            "    try {\n" +
            "      DaoRigin.deleteCommentById(commentId);\n" +
            "      console.log('已删除显示备注:', commentId);\n" +
            "    } catch (error) {\n" +
            "      console.error('删除显示备注失败:', commentId, error);\n" +
            "    }\n" +
            "  })\n" +
            "})";

        try {
            console.log('开始从显示中删除备注:', commentIds);
            const result = await this.executeEditorScript(script, {
                commentList: commentIds
            });
            console.log('从显示中删除备注完成:', result);
            return result;
        } catch (error) {
            console.error('从显示中删除备注失败:', error);
            throw error;
        }
    }

    /**
     * 插入文档
     * @param content
     */
    public async applySuggestion(content:string):Promise<any>{
        const script: string = "\n "+
            " DaoRigin.handleAction(function() { \n"+
            "   var   range = DaoRigin.getSelectRange() \n"+
            "   if (!range) {\n"+
            // "        console.log('没有找到定位点', operation)\n"+
            "        return\n"+
            "     }\n"+
            "     DaoRigin.scrollToCursor()\n"+
            "     range.AddTextNew(content)\n"+
            "  })\n"
        return this.executeEditorScript(script, {
            content: content
        });
    }

    /**
     * 保存文档
     */
    public saveDocument(){
      const script: string ="\nDaoRigin.save()";
      this.executeEditorScript(script);
    }


}

export default OnlyOfficeManager; 