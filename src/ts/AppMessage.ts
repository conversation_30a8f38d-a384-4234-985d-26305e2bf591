/**
 * AppMessage.ts
 * 用于处理跨窗口/iframe通信的工具类
 */

/**
 * 自定义Promise类，用于创建可从外部解析的Promise
 */
export class PromiseClass {
  public promise: any;
  public resolve: any;
  public reject: any;

  constructor() {
    var _this = this;
    this.promise = new Promise((function(e, n) {
      _this.resolve = e;
      _this.reject = n;
    }));
  }
}



/**
 * AppMessage类初始化参数
 */
interface AppMessageOptions {
  isPlugin?: boolean;
  iframe?: HTMLIFrameElement;
}

/**
 * 跨窗口/iframe通信管理器
 * 用于在主窗口和iframe之间执行脚本和传递消息
 */
export default class AppMessage {
  // 是否在插件环境中运行
  private isPlugin: boolean = false;
  // iframe元素引用
  private iframe: HTMLIFrameElement | null = null;
  // 存储待处理的Promise映射
  private deferredMap: Record<string, PromiseClass> = {};

  /**
   * 构造函数
   * @param options - 配置选项
   */
  constructor(options: AppMessageOptions = {}) {
    this.isPlugin = options.isPlugin || false;
    this.iframe = options.iframe || null;
    this.deferredMap = {};
    this.init();
  }

  /**
   * 初始化消息监听器
   * 处理从其他窗口接收的消息
   */
  private init() {
    const _this = this;
    window.addEventListener("message", (function(e: MessageEvent<any>) {
      if (e.data && e.data.eventId) {
        const n = _this.deferredMap[e.data.eventId];
        n && n.resolve(e.data.result);
      }
    }));
  }

  /**
   * 在目标窗口执行脚本
   * @param scriptText - 要执行的脚本文本
   * @param data - 要传递的附加数据
   * @returns 脚本执行结果的Promise
   */
  public executeScript(scriptText: string, data: any = null) {
    const _eventId = Math.random();
    let r = null;
    // @ts-ignore
    return r = this.iframe ? this.iframe.contentWindow : this.isPlugin ? window.parent : document.querySelector("[name=frameEditor]").contentWindow,
      r.postMessage({
        script: scriptText,
        eventId: _eventId,
        data: data
      }, "*"),
      this.deferredMap["reply#" + _eventId] = new PromiseClass(),
      this.deferredMap["reply#" + _eventId].promise;
  }
} 