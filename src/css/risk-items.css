/**
 * 风险项管理器样式文件
 * 为 RiskItemsManager 提供现代化的 UI 样式
 */

/* 主容器样式 */
.risk-items-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 工具栏样式 */
.risk-items-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left .title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.count-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-refresh {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-refresh:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.3);
}

.btn-jump {
  background-color: #3b82f6;
  color: white;
}

.btn-jump:hover {
  background-color: #2563eb;
}

.btn-delete {
  background-color: #ef4444;
  color: white;
}

.btn-delete:hover {
  background-color: #dc2626;
}

.btn-page {
  background-color: white;
  color: #374151;
  border: 1px solid #d1d5db;
  min-width: 40px;
  justify-content: center;
}

.btn-page:hover {
  background-color: #f3f4f6;
}

.btn-page.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 筛选器样式 */
.risk-items-filters {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  margin-right: 4px;
}

.checkbox-group {
  display: flex;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  margin: 0;
}

.sort-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: white;
}

.sort-direction-btn {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.sort-direction-btn:hover {
  background-color: #e5e7eb;
}

/* 等级徽章样式 */
.level-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.level-critical {
  background-color: #fee2e2;
  color: #991b1b;
}

.level-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.level-info {
  background-color: #dbeafe;
  color: #1e40af;
}

.level-unknown {
  background-color: #f3f4f6;
  color: #6b7280;
}

.index-badge {
  background-color: #e5e7eb;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 风险项列表样式 */
.risk-items-list-container {
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
  padding: 8px;
}

.risk-items-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.risk-item-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
  border-left: 4px solid #e5e7eb;
}

.risk-item-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.risk-item-card.level-1 {
  border-left-color: #ef4444;
}

.risk-item-card.level-2 {
  border-left-color: #f59e0b;
}

.risk-item-card.level-3 {
  border-left-color: #3b82f6;
}

.risk-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.header-left .name {
  font-weight: 600;
  color: #111827;
  margin-left: 8px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.risk-item-content {
  padding: 20px;
}

.risk-item-content > div:not(:last-child) {
  margin-bottom: 16px;
}

/* 内容区域样式 */
.hint-section h4,
.suggestion-section h4,
.quote-section h4,
.knowledge-section h4,
.laws-section h4,
.cases-section h4 {
  margin: 0 0 8px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.hint-section p,
.suggestion-section p,
.knowledge-section p {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
}

.quote-section blockquote {
  margin: 0;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-left: 4px solid #3b82f6;
  font-style: italic;
  color: #6b7280;
  border-radius: 0 4px 4px 0;
}

/* 元信息样式 */
.meta-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid #e5e7eb;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 60px;
}

.meta-value {
  font-size: 0.875rem;
  color: #374151;
}

.adoption-status.adopted {
  color: #059669;
  font-weight: 500;
}

.adoption-status.pending {
  color: #d97706;
  font-weight: 500;
}

/* 关联标签样式 */
.relation-tag {
  display: inline-block;
  background-color: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-right: 6px;
  margin-bottom: 4px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .risk-items-container {
    padding: 12px;
  }
  
  .risk-items-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left {
    justify-content: center;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-group,
  .sort-group {
    justify-content: center;
  }
  
  .checkbox-group {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .risk-item-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-left {
    justify-content: center;
  }
  
  .header-right {
    justify-content: center;
  }
  
  .meta-info {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .toolbar-left .title {
    font-size: 1.25rem;
  }
  
  .risk-item-card {
    margin: 0 -4px;
  }
  
  .risk-item-content {
    padding: 16px;
  }
} 