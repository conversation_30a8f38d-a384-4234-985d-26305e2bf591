<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RiskItemsManager 示例</title>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/onlyoffice-api@6.4.1/dist/api.js"></script>
    <!-- 引入主脚本，它会将 RiskItemsManager 和 OnlyOfficeManager 类暴露到全局 -->
    <script type="module" src="/src/main.ts"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
        }
        
        .editor-container {
            flex: 2;
            height: 100%;
            border-right: 1px solid #e0e0e0;
        }
        
        .risk-items-container {
            flex: 1;
            height: 100%;
            overflow-y: auto;
            padding: 0 10px;
            background-color: #f9f9f9;
        }
        
        .risk-items-header {
            position: sticky;
            top: 0;
            background-color: #f9f9f9;
            padding: 15px 0;
            border-bottom: 1px solid #e0e0e0;
            z-index: 10;
        }
        
        .risk-items-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .filters {
            margin: 15px 0;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .filter-group {
            margin-bottom: 10px;
        }
        
        .filter-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-checkbox {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        
        .filter-checkbox input {
            margin-right: 4px;
        }
        
        .search-box {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-top: 15px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
        }
        
        .container-title {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .container-count {
            background-color: #eee;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .container-toggle {
            color: #666;
            font-size: 18px;
            transition: transform 0.2s;
        }
        
        .container-toggle.expanded {
            transform: rotate(180deg);
        }
        
        .risk-item-card {
            margin: 10px 0;
            padding: 12px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .risk-item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .risk-item-title {
            font-weight: 600;
            color: #333;
        }
        
        .risk-level {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .level-critical {
            background-color: #ffebee;
            color: #d32f2f;
        }
        
        .level-warning {
            background-color: #fff8e1;
            color: #ff8f00;
        }
        
        .level-info {
            background-color: #e8f5e9;
            color: #388e3c;
        }
        
        .risk-item-content {
            margin-bottom: 10px;
            color: #555;
        }
        
        .risk-item-quote {
            margin: 10px 0;
            padding: 8px;
            background-color: #f5f5f5;
            border-left: 3px solid #ddd;
            color: #666;
            font-style: italic;
        }
        
        .risk-item-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        
        .action-button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background-color: #1976d2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1565c0;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .btn-secondary:hover {
            background-color: #e0e0e0;
        }
        
        .btn-danger {
            background-color: #f44336;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        
        .risk-item-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed #ddd;
        }
        
        .details-row {
            display: flex;
            margin-bottom: 5px;
        }
        
        .details-label {
            flex: 1;
            color: #666;
        }
        
        .details-value {
            flex: 2;
            color: #333;
        }
        
        .highlight-item {
            animation: highlight-pulse 2s;
        }
        
        @keyframes highlight-pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 230, 0, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 230, 0, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 230, 0, 0); }
        }
        
        .comment-content {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        
        .comment-textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 60px;
            margin-bottom: 8px;
        }
        
        .demo-controls {
            padding: 15px;
            background-color: #f0f0f0;
            border-bottom: 1px solid #ddd;
        }
        
        .demo-note {
            margin-top: 10px;
            padding: 10px;
            background-color: #fffde7;
            border-left: 4px solid #ffd600;
            font-size: 14px;
            color: #5d4037;
        }
    </style>
</head>
<body>
    <div class="editor-container" id="onlyoffice-container">
        <!-- OnlyOffice 编辑器将在这里初始化 -->
        <div class="demo-controls">
            <h2>OnlyOffice 编辑器示例</h2>
            <p>在实际应用中，这里会显示 OnlyOffice 编辑器</p>
            <button id="loadDataBtn" class="action-button btn-primary">模拟加载风险项数据</button>
            <button id="jumpToItemBtn" class="action-button btn-secondary">模拟跳转到风险项</button>
            
            <div class="demo-note">
                注意：这是一个演示页面，OnlyOffice 编辑器需要服务端支持才能完全初始化。
                在这个示例中，我们只模拟了数据流和交互。
            </div>
        </div>
    </div>
    
    <div class="risk-items-container" id="risk-items-container" x-data="riskItemsData">
        <div class="risk-items-header">
            <h2 class="risk-items-title">风险项管理</h2>
            
            <div class="filters">
                <input type="text" class="search-box" placeholder="搜索风险项..." x-model="filters.searchText" @input="applyFilters()">
                
                <div class="filter-group">
                    <div class="filter-title">风险等级</div>
                    <div class="filter-options">
                        <label class="filter-checkbox">
                            <input type="checkbox" value="1" x-model="filters.level" @change="applyFilters()">
                            <span>严重</span>
                        </label>
                        <label class="filter-checkbox">
                            <input type="checkbox" value="2" x-model="filters.level" @change="applyFilters()">
                            <span>一般</span>
                        </label>
                        <label class="filter-checkbox">
                            <input type="checkbox" value="3" x-model="filters.level" @change="applyFilters()">
                            <span>建议</span>
                        </label>
                    </div>
                </div>
                
                <div class="filter-group">
                    <div class="filter-title">当事人类型</div>
                    <div class="filter-options">
                        <label class="filter-checkbox">
                            <input type="checkbox" value="1" x-model="filters.partyType" @change="applyFilters()">
                            <span>甲乙双方</span>
                        </label>
                        <label class="filter-checkbox">
                            <input type="checkbox" value="2" x-model="filters.partyType" @change="applyFilters()">
                            <span>甲方</span>
                        </label>
                        <label class="filter-checkbox">
                            <input type="checkbox" value="3" x-model="filters.partyType" @change="applyFilters()">
                            <span>乙方</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 缺失条款容器 -->
        <div>
            <div class="container-header" @click="toggleMissingClauseContainer()">
                <div class="container-title">
                    缺失条款
                    <span class="container-count" x-text="missingClauseItems.length"></span>
                </div>
                <div class="container-toggle" :class="{ 'expanded': isMissingClauseContainerExpanded }">
                    ▼
                </div>
            </div>
            
            <div x-show="isMissingClauseContainerExpanded">
                <template x-for="item in missingClauseItems" :key="item.commentId">
                    <div class="risk-item-card" 
                         :data-comment-id="item.commentId"
                         :class="{ 'highlight-item': isItemSelected(item) }">
                        <div class="risk-item-header">
                            <div class="risk-item-title" x-text="item.name || '未命名条款'"></div>
                            <div class="risk-level" :class="getLevelClass(item.level)" x-text="getLevelName(item.level)"></div>
                        </div>
                        
                        <div class="risk-item-content" x-text="item.hint"></div>
                        
                        <div class="risk-item-actions">
                            <button class="action-button btn-primary" @click="insertText(item)">插入建议文本</button>
                            <button class="action-button btn-secondary" @click="jumpToComment(item)">跳转到位置</button>
                            <button class="action-button btn-secondary" @click="toggleCommentContent(item)">
                                <span x-text="isCommentContentVisible(item) ? '隐藏备注' : '显示备注'"></span>
                            </button>
                        </div>
                        
                        <div x-show="isCommentContentVisible(item)" class="comment-content">
                            <textarea class="comment-textarea" x-model="item.itemNote.content" placeholder="添加备注..."></textarea>
                            <div class="risk-item-actions">
                                <button class="action-button btn-primary" @click="saveItem(item)">保存备注</button>
                            </div>
                        </div>
                        
                        <div class="risk-item-details">
                            <div class="details-row">
                                <div class="details-label">建议文本：</div>
                                <div class="details-value" x-text="item.suggestion || '无'"></div>
                            </div>
                            <div class="details-row" x-show="showKnowledgeSource">
                                <div class="details-label">知识来源：</div>
                                <div class="details-value" x-text="item.knowledgeSource || '无'"></div>
                            </div>
                        </div>
                    </div>
                </template>
                
                <div x-show="missingClauseItems.length === 0" style="padding: 20px; text-align: center; color: #666;">
                    没有找到缺失条款
                </div>
            </div>
        </div>
        
        <!-- 风险提示容器 -->
        <div>
            <div class="container-header" @click="toggleRiskPromptContainer()">
                <div class="container-title">
                    风险提示
                    <span class="container-count" x-text="riskPromptItems.length"></span>
                </div>
                <div class="container-toggle" :class="{ 'expanded': isRiskPromptContainerExpanded }">
                    ▼
                </div>
            </div>
            
            <div x-show="isRiskPromptContainerExpanded">
                <template x-for="item in riskPromptItems" :key="item.commentId">
                    <div class="risk-item-card" 
                         :data-comment-id="item.commentId"
                         :class="{ 'highlight-item': isItemSelected(item) }">
                        <div class="risk-item-header">
                            <div class="risk-item-title" x-text="item.name || '风险提示'"></div>
                            <div class="risk-level" :class="getLevelClass(item.level)" x-text="getLevelName(item.level)"></div>
                        </div>
                        
                        <div class="risk-item-content" x-text="item.hint"></div>
                        
                        <div class="risk-item-quote" x-show="item.quoteText" x-text="item.quoteText"></div>
                        
                        <div class="risk-item-actions">
                            <button class="action-button btn-secondary" @click="jumpToComment(item)">跳转到位置</button>
                            <button class="action-button btn-secondary" @click="toggleCommentContent(item)">
                                <span x-text="isCommentContentVisible(item) ? '隐藏备注' : '显示备注'"></span>
                            </button>
                            <button class="action-button btn-danger" @click="deleteComment(item)">删除</button>
                        </div>
                        
                        <div x-show="isCommentContentVisible(item)" class="comment-content">
                            <textarea class="comment-textarea" x-model="item.itemNote.content" placeholder="添加备注..."></textarea>
                            <div class="risk-item-actions">
                                <button class="action-button btn-primary" @click="saveItem(item)">保存备注</button>
                            </div>
                        </div>
                        
                        <div class="risk-item-details" x-show="item.relationLaws && item.relationLaws.length > 0 && showRelationLaws">
                            <div class="details-row">
                                <div class="details-label">关联法规：</div>
                                <div class="details-value">
                                    <template x-for="law in item.relationLaws" :key="law.lawId">
                                        <div x-text="law.lawName"></div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                
                <div x-show="riskPromptItems.length === 0" style="padding: 20px; text-align: center; color: #666;">
                    没有找到风险提示
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟数据，实际应用中应从 OnlyOfficeManager 获取
        const mockRiskItems = [
            {
                commentId: "comment-001",
                objectId: "obj-001",
                type: "missingClause",
                level: 1,
                name: "保密条款",
                hint: "合同中缺少保密条款，建议添加",
                suggestion: "双方应对在合作过程中获知的对方的商业秘密和其他保密信息承担保密义务。除非法律要求或得到对方书面同意，任何一方不得向第三方披露上述信息。",
                knowledgeSource: "《合同法》第四十三条",
                partyType: 1,
                role: "legal",
                itemNote: { content: "", status: 0 }
            },
            {
                commentId: "comment-002",
                objectId: "obj-002",
                type: "missingClause",
                level: 2,
                name: "违约责任条款",
                hint: "合同中缺少明确的违约责任条款",
                suggestion: "任何一方违反本合同约定，应向守约方支付合同总金额 10% 的违约金。如违约金不足以弥补守约方损失的，违约方还应赔偿守约方的实际损失。",
                knowledgeSource: "《合同法》第一百零七条",
                partyType: 1,
                role: "legal",
                itemNote: { content: "", status: 0 }
            },
            {
                commentId: "comment-003",
                objectId: "obj-003",
                type: "riskPrompt",
                level: 1,
                name: "付款条件风险",
                hint: "付款条件对甲方过于不利，建议重新协商",
                quoteText: "乙方有权在交付产品前要求甲方支付全部款项，否则有权拒绝交付。",
                partyType: 2,
                role: "finance",
                relationLaws: [
                    { lawId: "law-001", lawName: "《合同法》第六十六条", content: "当事人互负债务，没有先后履行顺序的，应当同时履行。" }
                ],
                itemNote: { content: "已与对方协商，对方同意修改为分期付款", status: 1 }
            },
            {
                commentId: "comment-004",
                objectId: "obj-004",
                type: "riskPrompt",
                level: 2,
                name: "知识产权条款",
                hint: "知识产权归属不明确，可能导致后续纠纷",
                quoteText: "双方在合作过程中产生的知识产权归双方共有。",
                partyType: 3,
                role: "legal",
                relationLaws: [
                    { lawId: "law-002", lawName: "《专利法》第八条", content: "专利申请权和专利权可以转让。" }
                ],
                itemNote: { content: "", status: 0 }
            },
            {
                commentId: "comment-005",
                objectId: "obj-005",
                type: "riskPrompt",
                level: 3,
                name: "管辖权条款",
                hint: "建议明确约定争议解决方式和管辖法院",
                quoteText: "",
                partyType: 1,
                role: "legal",
                itemNote: { content: "", status: 0 }
            }
        ];

        // 使用真实的 OnlyOfficeManager 和 RiskItemsManager 类
        document.addEventListener('DOMContentLoaded', function() {
            // 等待 main.ts 将类暴露到全局
            const initApp = () => {
                if (!window.OnlyOfficeManager || !window.RiskItemsManager) {
                    console.log('等待 OnlyOfficeManager 和 RiskItemsManager 类加载...');
                    setTimeout(initApp, 100);
                    return;
                }
                
                console.log('OnlyOfficeManager 和 RiskItemsManager 类已加载');
                
                // 创建配置对象
                const config = {
                    containerId: 'onlyoffice-container',
                    rightMenuId: 'risk-items-container',
                    config: {
                        documentType: 'word',
                        document: {
                            fileType: 'docx',
                            key: 'demo-document',
                            title: '示例合同.docx',
                            url: 'https://example.com/demo-contract.docx'
                        },
                        editorConfig: {
                            mode: 'edit',
                            lang: 'zh-CN'
                        }
                    },
                    isShowComment: true,
                    isDevelopment: true,
                    subscribeRoles: 'legal,finance,reviewer',
                    riskLevels: { critical: 1, warning: 2, info: 3 },
                    relationUrl: 'https://example.com/api/relations',
                    showSign: true,
                    showKnowledgeSource: true
                };
                
                // 创建回调函数
                const callbacks = {
                    onItemJump: async (item) => {
                        console.log(`回调: onItemJump 被调用，objectId: ${item.objectId}`);
                        return onlyOfficeManager.scrollToCommentByObjectId(item.objectId);
                    },
                    onItemDelete: async (item) => {
                        console.log(`回调: onItemDelete 被调用，commentId: ${item.commentId}`);
                        return onlyOfficeManager.deleteCommentByCommentId([item.commentId]);
                    },
                    onTextInsert: async (hint, item) => {
                        console.log(`回调: onTextInsert 被调用，插入文本: ${hint.substring(0, 30)}...`);
                        return onlyOfficeManager.applySuggestion(hint);
                    },
                    onSaveItem: async (item) => {
                        console.log(`回调: onSaveItem 被调用，保存风险项: ${item.commentId}`);
                        // 模拟保存成功
                        return true;
                    }
                };
                
                try {
                    // 初始化管理器实例
                    const onlyOfficeManager = new window.OnlyOfficeManager(config);
                    const riskItemsManager = new window.RiskItemsManager(config, callbacks);
                    
                    // 初始化编辑器和风险项管理器
                    onlyOfficeManager.init();
                    riskItemsManager.init();
                    
                    // 监听自定义事件
                    document.addEventListener('onlyoffice-comments-loaded', function(event) {
                        console.log('捕获到 onlyoffice-comments-loaded 事件:', event.detail);
                        
                        // 从事件中获取风险项数据
                        if (event.detail && event.detail.docData) {
                            // 使用真实数据
                            riskItemsManager.setData(event.detail.docData);
                            console.log('已从 OnlyOffice 加载风险项数据，共', event.detail.docData.length, '项');
                        } else {
                            // 如果没有获取到真实数据，则使用模拟数据
                            riskItemsManager.setData(mockRiskItems);
                            console.log('未从事件中获取到数据，使用模拟数据');
                        }
                    });
                    
                    // 保留加载按钮的事件监听，用于手动测试
                    document.getElementById('loadDataBtn').addEventListener('click', function() {
                        // 手动加载风险项数据
                        riskItemsManager.setData(mockRiskItems);
                        console.log('已手动加载模拟数据');
                    });
                    
                    document.getElementById('jumpToItemBtn').addEventListener('click', function() {
                        // 跳转到风险项
                        if (mockRiskItems.length > 0) {
                            const randomIndex = Math.floor(Math.random() * mockRiskItems.length);
                            const randomItem = mockRiskItems[randomIndex];
                            console.log(`跳转到风险项: ${randomItem.name || randomItem.commentId}`);
                            riskItemsManager.scrollToItemByObjectId(randomItem.objectId);
                        } else {
                            console.log('没有可跳转的风险项');
                        }
                    });
                    
                    // 监听自定义事件
                    document.addEventListener('item-jump', function(event) {
                        console.log('捕获到 item-jump 事件:', event.detail);
                        onlyOfficeManager.scrollToCommentByObjectId(event.detail.objectId);
                    });
                    
                    document.addEventListener('text-insert', function(event) {
                        console.log('捕获到 text-insert 事件:', event.detail);
                        onlyOfficeManager.applySuggestion(event.detail.hint);
                    });
                    
                    document.addEventListener('item-save', function(event) {
                        console.log('捕获到 item-save 事件:', event.detail);
                    });
                    
                    document.addEventListener('onlyoffice-comment-clicked', function(event) {
                        console.log('捕获到 onlyoffice-comment-clicked 事件:', event.detail);
                        riskItemsManager.scrollToItemByObjectId(event.detail.firstCommentObjectId);
                    });
                    
                    console.log('应用初始化完成');
                } catch (error) {
                    console.error('初始化失败:', error);
                    document.querySelector('.demo-note').innerHTML += `<p style="color: red">初始化错误: ${error.message}</p>`;
                }
            };
            
            // 开始初始化
            initApp();
        });
    </script>
</body>
</html> 